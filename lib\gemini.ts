import { GoogleGenAI } from "@google/genai";

// Initialize the Gemini AI client
const ai = new GoogleGenAI({
  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY || "",
});

// Add error handling and logging for debugging
const validateApiKey = () => {
  const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY;
  if (!apiKey) {
    console.error("Gemini API key is not set. Please check your environment variables.");
    return false;
  }
  return true;
};

export interface AIToolSuggestion {
  name: string;
  description: string;
  url: string;
  category: string;
}

export interface WorkflowTask {
  title: string;
  description: string;
  minutes: number;
  frequency: string;
  difficulty: number;
  painPoint?: boolean;
}

export async function getAISuggestions(taskDescription: string): Promise<AIToolSuggestion[]> {
  if (!validateApiKey()) {
    throw new Error("API key not configured");
  }

  try {
    console.log("Getting AI suggestions for task:", taskDescription);

    const prompt = `
You are an AI workflow assistant. Given a task description, suggest 5 relevant AI tools that could help accomplish this task.

Task: "${taskDescription}"

Please respond with a JSON array of tools in this exact format:
[
  {
    "name": "Tool Name",
    "description": "Brief description of how this tool helps with the task",
    "url": "https://example.com",
    "category": "Category (e.g., Writing, Design, Analysis, etc.)"
  }
]

Focus on popular, well-known AI tools that are actually available and useful for the given task.`;

    // Add retry logic for overloaded model
    let retries = 3;
    let lastError;

    while (retries > 0) {
      try {
        const response = await ai.models.generateContent({
          model: "gemini-2.5-flash",
          contents: prompt,
        });

        const responseText = response.text;
        
        if (!responseText) {
          throw new Error("No response text received from AI");
        }
        
        // Try to extract JSON from the response with better error handling
        console.log("Raw Gemini response:", responseText);
        
        // Look for JSON array in the response - try multiple patterns
        let jsonMatch = responseText.match(/\[[\s\S]*?\]/);
        
        if (!jsonMatch) {
          // Try to find JSON without markdown code blocks
          jsonMatch = responseText.replace(/```json\s*|\s*```/g, '').match(/\[[\s\S]*?\]/);
        }
        
        if (jsonMatch) {
          let jsonString = jsonMatch[0];
          
          // More aggressive JSON cleaning
          jsonString = jsonString
            .replace(/,\s*([}\]])/g, '$1')  // Remove trailing commas
            .replace(/([{,]\s*)(\w+):/g, '$1"$2":')  // Add quotes to unquoted keys
            .replace(/:\s*([^",\[\]{}]+?)(?=\s*[,}\]])/g, (match, value) => {
              const trimmed = value.trim();
              // Don't quote numbers, booleans, null, or already quoted strings
              if (trimmed === 'true' || trimmed === 'false' || trimmed === 'null' || 
                  /^\d+(\.\d+)?$/.test(trimmed) || trimmed.startsWith('"')) {
                return `: ${trimmed}`;
              }
              // Quote unquoted strings and escape internal quotes
              return `: "${trimmed.replace(/"/g, '\\"')}"`;
            })
            .replace(/\n/g, ' ')  // Remove newlines
            .replace(/\s+/g, ' '); // Normalize whitespace
          
          console.log("Cleaned JSON string:", jsonString);
          
          try {
            const suggestions = JSON.parse(jsonString);
            if (Array.isArray(suggestions) && suggestions.length > 0) {
              const validSuggestions = suggestions.filter(s => 
                s && typeof s === 'object' && s.name && s.description && s.url && s.category
              );
              
              if (validSuggestions.length > 0) {
                console.log(`✅ Successfully parsed ${validSuggestions.length} valid suggestions from Gemini`);
                return validSuggestions;
              }
            }
          } catch (parseError) {
            console.error("❌ JSON parse error:", parseError);
            console.error("Failed JSON string:", jsonString);
          }
        }
        
        console.log("⚠️ JSON parsing failed, using fallback suggestions");

        throw new Error("Failed to parse Gemini response");
      } catch (error: unknown) {
        lastError = error;
        if (error && typeof error === 'object' && 'error' in error) {
          const apiError = error as { error: { code: number } };
          if (apiError.error?.code === 503) {
            console.warn(`Gemini API overloaded, retrying... (${retries} attempts left)`);
            retries--;
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, (3 - retries) * 1000));
            continue;
          }
        }
        throw error;
      }
    }

    console.error("Gemini API failed after retries:", lastError);
    // Don't throw error, fall through to fallback suggestions
  } catch (error) {
    console.error("Gemini API failed:", error);
  }
  
  // Return hardcoded fallback suggestions if API fails
  console.log("🔄 Using fallback AI tool suggestions");
  return getTaskSpecificSuggestions(taskDescription);
}

function getTaskSpecificSuggestions(taskDescription: string): AIToolSuggestion[] {
  const task = taskDescription.toLowerCase();
  
  // Return task-specific suggestions based on keywords
  if (task.includes('write') || task.includes('content') || task.includes('blog') || task.includes('newsletter')) {
    return [
      {
        name: "ChatGPT",
        description: "AI writing assistant for content creation and editing",
        url: "https://chat.openai.com",
        category: "Writing"
      },
      {
        name: "Grammarly",
        description: "AI-powered writing assistant for grammar and style",
        url: "https://grammarly.com",
        category: "Writing"
      },
      {
        name: "Jasper AI",
        description: "AI content creation platform for marketing copy",
        url: "https://jasper.ai",
        category: "Writing"
      },
      {
        name: "Copy.ai",
        description: "AI copywriting tool for marketing content",
        url: "https://copy.ai",
        category: "Writing"
      },
      {
        name: "Notion AI",
        description: "AI-powered workspace for writing and organization",
        url: "https://notion.so",
        category: "Productivity"
      }
    ];
  }
  
  if (task.includes('design') || task.includes('image') || task.includes('visual') || task.includes('graphic')) {
    return [
      {
        name: "Canva AI",
        description: "AI design tools for creating visual content",
        url: "https://canva.com",
        category: "Design"
      },
      {
        name: "Midjourney",
        description: "AI image generation for creative visuals",
        url: "https://midjourney.com",
        category: "Design"
      },
      {
        name: "DALL-E",
        description: "AI image generator by OpenAI",
        url: "https://openai.com/dall-e-2",
        category: "Design"
      },
      {
        name: "Figma AI",
        description: "AI-powered design collaboration platform",
        url: "https://figma.com",
        category: "Design"
      },
      {
        name: "Adobe Firefly",
        description: "AI creative tools for design and content",
        url: "https://firefly.adobe.com",
        category: "Design"
      }
    ];
  }
  
  if (task.includes('video') || task.includes('youtube') || task.includes('stream')) {
    return [
      {
        name: "Descript",
        description: "AI video editing with transcription and voice cloning",
        url: "https://descript.com",
        category: "Video"
      },
      {
        name: "Runway ML",
        description: "AI video generation and editing tools",
        url: "https://runwayml.com",
        category: "Video"
      },
      {
        name: "Loom AI",
        description: "AI-powered screen recording and video messaging",
        url: "https://loom.com",
        category: "Video"
      },
      {
        name: "Synthesia",
        description: "AI video creation with virtual presenters",
        url: "https://synthesia.io",
        category: "Video"
      },
      {
        name: "Pictory",
        description: "AI video creation from text content",
        url: "https://pictory.ai",
        category: "Video"
      }
    ];
  }
  
  if (task.includes('data') || task.includes('analysis') || task.includes('research')) {
    return [
      {
        name: "Claude",
        description: "AI assistant for data analysis and research",
        url: "https://claude.ai",
        category: "Analysis"
      },
      {
        name: "Perplexity AI",
        description: "AI research assistant with real-time information",
        url: "https://perplexity.ai",
        category: "Research"
      },
      {
        name: "DataRobot",
        description: "AI platform for automated machine learning",
        url: "https://datarobot.com",
        category: "Analysis"
      },
      {
        name: "Tableau AI",
        description: "AI-powered data visualization and analytics",
        url: "https://tableau.com",
        category: "Analysis"
      },
      {
        name: "MonkeyLearn",
        description: "AI text analysis and data extraction",
        url: "https://monkeylearn.com",
        category: "Analysis"
      }
    ];
  }
  
  if (task.includes('social') || task.includes('marketing') || task.includes('post')) {
    return [
      {
        name: "Buffer AI",
        description: "AI social media scheduling and content creation",
        url: "https://buffer.com",
        category: "Marketing"
      },
      {
        name: "Hootsuite AI",
        description: "AI-powered social media management",
        url: "https://hootsuite.com",
        category: "Marketing"
      },
      {
        name: "Later AI",
        description: "AI social media content planning and scheduling",
        url: "https://later.com",
        category: "Marketing"
      },
      {
        name: "Sprout Social AI",
        description: "AI social media analytics and engagement",
        url: "https://sproutsocial.com",
        category: "Marketing"
      },
      {
        name: "Brandwatch",
        description: "AI social media monitoring and insights",
        url: "https://brandwatch.com",
        category: "Marketing"
      }
    ];
  }
  
  // Default general AI tools
  return [
    {
      name: "ChatGPT",
      description: "AI assistant for general task completion and guidance",
      url: "https://chat.openai.com",
      category: "General AI"
    },
    {
      name: "Claude",
      description: "AI assistant for analysis and task breakdown",
      url: "https://claude.ai",
      category: "General AI"
    },
    {
      name: "Notion AI",
      description: "AI-powered workspace for task organization",
      url: "https://notion.so",
      category: "Productivity"
    },
    {
      name: "Grammarly",
      description: "AI-powered writing assistant for improving text quality",
      url: "https://grammarly.com",
      category: "Writing"
    },
    {
      name: "Canva AI",
      description: "AI design tools for creating visual content",
      url: "https://canva.com",
      category: "Design"
    }
  ];
}

export async function getWorkflowOptimizationSuggestions(tasks: string[]): Promise<string[]> {
  try {
    const prompt = `
You are a workflow optimization expert. Given this list of tasks, provide 3-5 specific suggestions to optimize the workflow:

Tasks: ${tasks.join(", ")}

Provide suggestions as a simple array of strings, each suggestion being actionable advice for improving the workflow efficiency.

Respond with a JSON array of strings:
["suggestion 1", "suggestion 2", "suggestion 3"]
`;

    const response = await ai.models.generateContent({
      model: "gemini-1.5-flash",
      contents: prompt,
    });

    const responseText = response.text;
    
    if (!responseText) {
      throw new Error("No response text received from AI");
    }
    
    // Try to extract JSON from the response
    const jsonMatch = responseText.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      const suggestions = JSON.parse(jsonMatch[0]);
      return suggestions;
    }

    // Fallback suggestions
    return [
      "Consider automating repetitive tasks",
      "Group similar tasks together for better efficiency",
      "Add time buffers between complex tasks",
      "Use AI tools to accelerate task completion"
    ];

  } catch (error) {
    console.error("Error getting workflow optimization suggestions:", error);
    
    return [
      "Consider automating repetitive tasks",
      "Group similar tasks together for better efficiency",
      "Add time buffers between complex tasks",
      "Use AI tools to accelerate task completion"
    ];
  }
}

export async function generateWorkflowFromDescription(description: string): Promise<WorkflowTask[]> {
  try {
    const prompt = `
You are a workflow automation expert. Break down this natural language description into a structured workflow of tasks.

Description: "${description}"

Please respond with a JSON array of tasks in this exact format:
[
  {
    "title": "Task Name",
    "description": "Detailed description of what this task involves",
    "minutes": 30,
    "frequency": "daily" or "weekly" or "monthly",
    "difficulty": 1-5 (1=very easy, 5=very hard),
    "painPoint": true/false (optional, only if this task is particularly challenging or frustrating)
  }
]

Rules:
- Break the workflow into 3-8 logical, sequential tasks
- Be specific and actionable with task titles
- Estimate realistic time requirements
- Identify pain points (difficult/frustrating tasks)
- Use appropriate frequency based on the description
`;

    const response = await ai.models.generateContent({
      model: "gemini-1.5-flash",
      contents: prompt,
    });

    const responseText = response.text;
    
    if (!responseText) {
      throw new Error("No response text received from AI");
    }
    
    // Try to extract JSON from the response
    const jsonMatch = responseText.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      const tasks = JSON.parse(jsonMatch[0]);
      return tasks;
    }

    // Fallback to mock implementation if AI fails
    return generateMockWorkflow(description);

  } catch (error) {
    console.error("Error generating workflow from description:", error);
    return generateMockWorkflow(description);
  }
}

function generateMockWorkflow(description: string): WorkflowTask[] {
  const mockTasks: WorkflowTask[] = [];
  
  if (description.toLowerCase().includes('email')) {
    mockTasks.push({
      title: 'Check emails',
      description: 'Review and respond to important emails',
      minutes: 15,
      frequency: 'daily',
      difficulty: 2
    });
  }
  
  if (description.toLowerCase().includes('newsletter')) {
    mockTasks.push({
      title: 'Write newsletter',
      description: 'Create engaging newsletter content',
      minutes: 45,
      frequency: 'weekly',
      difficulty: 4
    });
  }
  
  if (description.toLowerCase().includes('linkedin') || description.toLowerCase().includes('post')) {
    mockTasks.push({
      title: 'Post on LinkedIn',
      description: 'Share newsletter content on LinkedIn',
      minutes: 10,
      frequency: 'weekly',
      difficulty: 2
    });
  }
  
  if (description.toLowerCase().includes('zoom') || description.toLowerCase().includes('call') || description.toLowerCase().includes('meeting')) {
    mockTasks.push({
      title: 'Team meeting',
      description: 'Hop on Zoom call with marketing team',
      minutes: 60,
      frequency: 'daily',
      difficulty: 3
    });
  }
  
  if (description.toLowerCase().includes('youtube')) {
    mockTasks.push(
      {
        title: 'Research topics',
        description: 'Research trending topics for YouTube content',
        minutes: 30,
        frequency: 'weekly',
        difficulty: 3
      },
      {
        title: 'Write scripts',
        description: 'Create engaging video scripts',
        minutes: 60,
        frequency: 'weekly',
        difficulty: 4
      },
      {
        title: 'Record videos',
        description: 'Record high-quality video content',
        minutes: 120,
        frequency: 'weekly',
        difficulty: 4
      },
      {
        title: 'Edit videos',
        description: 'Edit and polish video content',
        minutes: 180,
        frequency: 'weekly',
        difficulty: 5
      },
      {
        title: 'Upload to YouTube',
        description: 'Upload videos and optimize for discovery',
        minutes: 20,
        frequency: 'weekly',
        difficulty: 2
      }
    );
  }
  
  if (description.toLowerCase().includes('design')) {
    mockTasks.push(
      {
        title: 'Find clients',
        description: 'Prospect and reach out to potential clients',
        minutes: 90,
        frequency: 'weekly',
        difficulty: 4,
        painPoint: true
      },
      {
        title: 'Create proposals',
        description: 'Write detailed project proposals',
        minutes: 45,
        frequency: 'weekly',
        difficulty: 3
      },
      {
        title: 'Design mockups',
        description: 'Create initial design concepts',
        minutes: 240,
        frequency: 'weekly',
        difficulty: 5
      },
      {
        title: 'Get feedback',
        description: 'Present designs and gather client feedback',
        minutes: 30,
        frequency: 'weekly',
        difficulty: 2
      },
      {
        title: 'Deliver final files',
        description: 'Prepare and deliver final design assets',
        minutes: 60,
        frequency: 'weekly',
        difficulty: 3
      }
    );
  }
  
  if (description.toLowerCase().includes('social media')) {
    mockTasks.push(
      {
        title: 'Plan content',
        description: 'Create content calendar and strategy',
        minutes: 60,
        frequency: 'weekly',
        difficulty: 4
      },
      {
        title: 'Create posts',
        description: 'Design and write social media posts',
        minutes: 120,
        frequency: 'weekly',
        difficulty: 3
      },
      {
        title: 'Schedule posts',
        description: 'Schedule content across platforms',
        minutes: 20,
        frequency: 'weekly',
        difficulty: 2
      },
      {
        title: 'Engage with comments',
        description: 'Respond to comments and messages',
        minutes: 30,
        frequency: 'daily',
        difficulty: 2
      },
      {
        title: 'Analyze performance',
        description: 'Review analytics and optimize strategy',
        minutes: 45,
        frequency: 'weekly',
        difficulty: 3
      }
    );
  }
  
  // If no specific tasks found, create a generic workflow
  if (mockTasks.length === 0) {
    mockTasks.push(
      {
        title: 'Plan workflow',
        description: 'Break down the described process into steps',
        minutes: 30,
        frequency: 'weekly',
        difficulty: 3
      },
      {
        title: 'Execute tasks',
        description: 'Complete the main activities described',
        minutes: 60,
        frequency: 'weekly',
        difficulty: 4
      },
      {
        title: 'Review results',
        description: 'Evaluate outcomes and plan improvements',
        minutes: 15,
        frequency: 'weekly',
        difficulty: 2
      }
    );
  }
  
  return mockTasks;
}
