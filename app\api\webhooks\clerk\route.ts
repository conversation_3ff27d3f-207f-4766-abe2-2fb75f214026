import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { WebhookEvent } from '@clerk/nextjs/server';
import { createSupabaseAdminClient } from '@/lib/supabase-server';
 
export async function POST(req: Request) {
  // Get the headers
  const headerPayload = await headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');
 
  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Error occured -- no svix headers', {
      status: 400
    });
  }
 
  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);
 
  // Create a new Svix instance with your webhook secret
  const wh = new Webhook(process.env.CLERK_WEBHOOK_SECRET || '');
 
  let evt: WebhookEvent;
 
  // Verify the webhook payload
  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return new Response('Error occured', {
      status: 400
    });
  }
 
  const eventType = evt.type;
  const supabase = createSupabaseAdminClient();
 
  if (eventType === 'user.created' || eventType === 'user.updated') {
    const { id, email_addresses, first_name, last_name, image_url } = evt.data;
    const primaryEmail = email_addresses?.[0]?.email_address;

    const { error } = await supabase.from('users').upsert({
      id: id,
      email: primaryEmail,
      full_name: `${first_name || ''} ${last_name || ''}`.trim(),
      avatar_url: image_url,
      clerk_id: id,
      updated_at: new Date().toISOString()
    });

    if (error) {
      console.error('Error syncing user to Supabase:', error);
      return new Response('Error syncing user', { status: 500 });
    }
  }
 
  return new Response('Success', { status: 200 });
} 