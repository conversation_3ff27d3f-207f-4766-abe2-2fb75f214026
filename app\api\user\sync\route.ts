import { createSupabaseAdminClient } from '@/lib/supabase-server';
import { currentUser } from '@clerk/nextjs/server';

export async function POST(req: Request) {
  try {
    const user = await currentUser();
    if (!user) {
      return new Response('Unauthorized', { status: 401 });
    }

    const data = await req.json();
    const supabase = createSupabaseAdminClient();
    
    const { error } = await supabase.from('users').upsert(
      {
        id: data.id,
        email: data.email,
        full_name: data.full_name,
        avatar_url: data.avatar_url,
        clerk_id: data.id,
        updated_at: new Date().toISOString()
      },
      {
        onConflict: 'clerk_id',
        ignoreDuplicates: false
      }
    );

    if (error) throw error;
    
    return new Response('Success', { status: 200 });
  } catch (error) {
    console.error('Error syncing user:', error);
    return new Response('Error syncing user', { status: 500 });
  }
} 