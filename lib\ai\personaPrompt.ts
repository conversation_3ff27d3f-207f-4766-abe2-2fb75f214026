import { GoogleGenAI } from "@google/genai";

if (!process.env.NEXT_PUBLIC_GEMINI_API_KEY && !process.env.GEMINI_API_KEY) {
  console.error("Gemini API key is missing. Please set the NEXT_PUBLIC_GEMINI_API_KEY or GEMINI_API_KEY environment variable.");
}

const ai = new GoogleGenAI({ 
  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY || "" 
});

const model = "gemini-2.5-flash";

export interface PersonaStyle {
  style: string;
  palette: string;
  tone: string;
  platforms: string[];
  visualElements: string[];
  contentThemes: string[];
}

export async function generatePersonaStyle(persona: string): Promise<PersonaStyle> {
  const prompt = `
You are an expert brand strategist and visual director.

Given a brand persona like "${persona}", describe:
- Visual style (e.g., minimal, bold, luxury, rustic)
- Preferred color palette description
- Typical tone of voice (e.g., soft, confident, playful, professional)
- Ideal platforms (e.g., Instagram, Pinterest, Reels, TikTok)
- Key visual elements that work well for this persona
- Content themes that resonate with this audience

Return ONLY in clean JSON like this:
{
  "style": "...",
  "palette": "...",
  "tone": "...",
  "platforms": ["..."],
  "visualElements": ["..."],
  "contentThemes": ["..."]
}`;

  try {
    const response = await ai.models.generateContent({
      model,
      contents: [{ text: prompt }],
    });

    const responseText = response.text.trim();
    
    // Clean up the response to extract JSON
    let jsonStr = responseText;
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }

    return JSON.parse(jsonStr);
  } catch (error) {
    console.error("Error generating persona style:", error);
    
    // Fallback response
    return {
      style: "modern and clean",
      palette: "neutral tones with accent colors",
      tone: "professional yet approachable",
      platforms: ["instagram", "linkedin"],
      visualElements: ["clean typography", "minimal layouts", "high-quality imagery"],
      contentThemes: ["behind-the-scenes", "product highlights", "customer stories"]
    };
  }
}

export async function analyzePersonaFromImage(base64Image: string, mimeType: string): Promise<PersonaStyle> {
  const imagePart = {
    inlineData: { mimeType, data: base64Image },
  };

  const textPart = {
    text: `You're a brand strategist analyzing this image to determine the brand persona.

Look at the visual elements, colors, style, and overall aesthetic to determine:
- What brand persona this represents (e.g., luxury, minimalist, bold, rustic)
- Visual style characteristics
- Color palette used
- Tone of voice this would suggest
- Best platforms for this aesthetic
- Visual elements that define this style
- Content themes that would work

Return ONLY in clean JSON format:
{
  "style": "...",
  "palette": "...",
  "tone": "...",
  "platforms": ["..."],
  "visualElements": ["..."],
  "contentThemes": ["..."]
}`,
  };

  try {
    const response = await ai.models.generateContent({
      model,
      contents: { parts: [imagePart, textPart] },
    });

    const responseText = response.text.trim();
    
    // Clean up the response to extract JSON
    let jsonStr = responseText;
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }

    return JSON.parse(jsonStr);
  } catch (error) {
    console.error("Error analyzing persona from image:", error);
    
    // Fallback response
    return {
      style: "contemporary and versatile",
      palette: "balanced color scheme",
      tone: "engaging and authentic",
      platforms: ["instagram", "facebook"],
      visualElements: ["balanced composition", "natural lighting", "authentic moments"],
      contentThemes: ["lifestyle content", "product in use", "brand values"]
    };
  }
}
