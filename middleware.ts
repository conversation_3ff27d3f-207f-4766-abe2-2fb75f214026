import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'

// Define protected routes
const isProtectedRoute = createRouteMatcher([
  '/create(.*)',  // Protect all create routes
  '/dashboard(.*)', // Protect all dashboard routes
  '/api/(.*)'  // Protect all API routes
])

// Define public API routes that should bypass protection
const isPublicApiRoute = createRouteMatcher([
  '/api/webhooks/clerk(.*)'  // Clerk webhook endpoints
])

export default clerkMiddleware(async (auth, req) => {
  // Check if it's a protected route and not a public API route
  if (isProtectedRoute(req) && !isPublicApiRoute(req)) {
    await auth.protect()
  }
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}