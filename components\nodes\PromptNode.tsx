'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Wand2, 
  Copy, 
  Heart,
  Edit3,
  Check,
  X,
  Sparkles,
  Image as ImageIcon,
  Video,
  Type,
  Target
} from 'lucide-react';

interface PromptNodeData {
  title: string;
  description?: string;
  type: 'visual' | 'reels' | 'caption' | 'ad';
  config?: {
    platforms?: string[];
    aspectRatios?: string[];
    cameraStyles?: string[];
  };
  generatedPrompt?: string;
  selectedPlatform?: string;
  selectedAspectRatio?: string;
}

const PROMPT_ICONS = {
  visual: ImageIcon,
  reels: Video,
  caption: Type,
  ad: Target
};

const PROMPT_COLORS = {
  visual: 'bg-purple-500',
  reels: 'bg-green-500',
  caption: 'bg-blue-500',
  ad: 'bg-orange-500'
};

export default function PromptNode({ data, selected, id }: NodeProps<PromptNodeData>) {
  const [isEditing, setIsEditing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPrompt, setGeneratedPrompt] = useState(data.generatedPrompt || '');
  const [selectedPlatform, setSelectedPlatform] = useState(data.selectedPlatform || '');
  const [selectedAspectRatio, setSelectedAspectRatio] = useState(data.selectedAspectRatio || '');

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleGenerate = async () => {
    setIsGenerating(true);
    
    // Simulate AI generation
    setTimeout(() => {
      const mockPrompt = `Professional ${data.type} prompt for ${selectedPlatform || 'social media'}: High-quality, brand-aligned content with ${selectedAspectRatio || '4:5'} aspect ratio, featuring elegant composition, natural lighting, and premium aesthetic that resonates with target audience.`;
      setGeneratedPrompt(mockPrompt);
      data.generatedPrompt = mockPrompt;
      setIsGenerating(false);
    }, 2000);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedPrompt);
    // Add toast notification here
  };

  const IconComponent = PROMPT_ICONS[data.type];
  const colorClass = PROMPT_COLORS[data.type];

  const getStatusColor = () => {
    if (generatedPrompt) return 'bg-green-500';
    return 'bg-gray-400';
  };

  return (
    <div className="relative workflow-node group">
      <Handle
        type="target"
        position={Position.Left}
        id="prompt-input"
        className="w-3 h-3 !bg-purple-500 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="prompt-output"
        className="w-3 h-3 !bg-purple-500 border-2 border-white"
      />
      
      <Card 
        className={`w-96 cursor-pointer transition-all duration-200 ${
          selected ? 'ring-2 ring-purple-500 shadow-lg' : 'shadow-md hover:shadow-lg'
        }`}
        onDoubleClick={handleDoubleClick}
      >
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-3">
            <div className={`w-10 h-10 ${colorClass} rounded-xl flex items-center justify-center`}>
              <IconComponent className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="text-lg font-semibold">{data.title}</span>
                <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
              </div>
              <p className="text-sm text-gray-600 font-normal">{data.description}</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setIsEditing(true);
              }}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <Edit3 className="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Configuration */}
          <div className="grid grid-cols-2 gap-3">
            {data.config?.platforms && (
              <div>
                <label className="text-xs font-medium text-gray-600 mb-1 block">Platform</label>
                <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                  <SelectTrigger className="h-8 text-sm">
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent>
                    {data.config.platforms.map((platform) => (
                      <SelectItem key={platform} value={platform}>
                        {platform.charAt(0).toUpperCase() + platform.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {data.config?.aspectRatios && (
              <div>
                <label className="text-xs font-medium text-gray-600 mb-1 block">Aspect Ratio</label>
                <Select value={selectedAspectRatio} onValueChange={setSelectedAspectRatio}>
                  <SelectTrigger className="h-8 text-sm">
                    <SelectValue placeholder="Select ratio" />
                  </SelectTrigger>
                  <SelectContent>
                    {data.config.aspectRatios.map((ratio) => (
                      <SelectItem key={ratio} value={ratio}>
                        {ratio}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Generate Button */}
          <Button
            onClick={(e) => {
              e.stopPropagation();
              handleGenerate();
            }}
            disabled={isGenerating}
            className="w-full"
            size="sm"
          >
            {isGenerating ? (
              <>
                <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Wand2 className="w-4 h-4 mr-2" />
                Generate {data.type.charAt(0).toUpperCase() + data.type.slice(1)}
              </>
            )}
          </Button>

          {/* Generated Prompt */}
          {generatedPrompt && (
            <div className="space-y-2">
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-800 leading-relaxed">
                  {generatedPrompt.length > 100 
                    ? `${generatedPrompt.substring(0, 100)}...` 
                    : generatedPrompt
                  }
                </p>
              </div>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    copyToClipboard();
                  }}
                  className="flex-1"
                >
                  <Copy className="w-3 h-3 mr-1" />
                  Copy
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Save to favorites
                  }}
                >
                  <Heart className="w-3 h-3" />
                </Button>
              </div>
            </div>
          )}

          {/* Configuration badges */}
          {(selectedPlatform || selectedAspectRatio) && (
            <div className="flex flex-wrap gap-1">
              {selectedPlatform && (
                <Badge variant="secondary" className="text-xs">
                  {selectedPlatform}
                </Badge>
              )}
              {selectedAspectRatio && (
                <Badge variant="outline" className="text-xs">
                  {selectedAspectRatio}
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Modal/Overlay */}
      {isEditing && (
        <div className="absolute inset-0 bg-white border-2 border-purple-500 rounded-lg shadow-xl z-10 p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Configure {data.type.charAt(0).toUpperCase() + data.type.slice(1)}</h3>
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsEditing(false)}
              >
                <Check className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsEditing(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="text-sm text-gray-600 space-y-2">
            <p>Configure {data.type} generation settings:</p>
            <ul className="text-xs space-y-1">
              <li>• Platform targeting</li>
              <li>• Aspect ratio optimization</li>
              <li>• Style preferences</li>
              <li>• Content parameters</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
