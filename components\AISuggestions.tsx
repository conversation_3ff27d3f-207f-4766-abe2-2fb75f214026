'use client';

import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ExternalLink, Sparkles, Loader2 } from "lucide-react";
import { useWorkflow } from "@/store/useWorkflow";
import { AIToolSuggestion } from "@/lib/gemini";

interface AISuggestionsProps {
  nodeId: string;
  taskDescription: string;
}

export default function AISuggestions({ nodeId, taskDescription }: AISuggestionsProps) {
  const { suggestions, isLoadingSuggestions, fetchAISuggestions } = useWorkflow();
  const nodeSuggestions = suggestions[nodeId] || [];

  const handleGetSuggestions = () => {
    fetchAISuggestions(nodeId, taskDescription);
  };

  const openTool = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <Card className="w-80 shadow-lg border-2 border-purple-200">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-purple-700">
          <Sparkles className="w-5 h-5" />
          AI Tool Suggestions
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {nodeSuggestions.length === 0 ? (
          <div className="text-center py-4">
            <p className="text-sm text-gray-600 mb-3">
              Get AI-powered tool recommendations for this task
            </p>
            <Button 
              onClick={handleGetSuggestions}
              disabled={isLoadingSuggestions}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isLoadingSuggestions ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Getting Suggestions...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Get AI Suggestions
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {nodeSuggestions.map((suggestion: AIToolSuggestion, index: number) => (
              <TooltipProvider key={index}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div 
                      className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => openTool(suggestion.url)}
                    >
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-sm text-gray-900">
                              {suggestion.name}
                            </h4>
                            <Badge variant="secondary" className="text-xs">
                              {suggestion.category}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-600 line-clamp-2">
                            {suggestion.description}
                          </p>
                        </div>
                        <ExternalLink className="w-4 h-4 text-gray-400 flex-shrink-0" />
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Click to open {suggestion.name}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleGetSuggestions}
              disabled={isLoadingSuggestions}
              className="w-full mt-2"
            >
              {isLoadingSuggestions ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Refreshing...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Refresh Suggestions
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
