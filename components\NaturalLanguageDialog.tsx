'use client';

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON><PERSON>, Mi<PERSON>, Loader2 } from "lucide-react";

interface NaturalLanguageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (description: string) => void;
}

export default function NaturalLanguageDialog({ 
  open, 
  onOpenChange, 
  onSubmit 
}: NaturalLanguageDialogProps) {
  const [description, setDescription] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!description.trim()) return;
    
    setIsGenerating(true);
    try {
      await onSubmit(description);
      setDescription("");
      onOpenChange(false);
    } catch (error) {
      console.error('Error generating workflow:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const examplePrompts = [
    "Every morning I check emails, write a newsletter, and post it on LinkedIn. Then I hop on a Zoom call with my marketing team.",
    "I run a YouTube channel. I research topics, write scripts, record videos, edit them, and post on YouTube + Shorts.",
    "I'm a freelance designer. I find clients, create proposals, design mockups, get feedback, and deliver final files.",
    "I manage social media. I plan content, create posts, schedule them, engage with comments, and analyze performance."
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-600" />
            Describe Your Workflow
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="text-sm text-gray-600">
            Simply describe your daily workflow in natural language, and we&apos;ll automatically create a visual flow with AI tool suggestions!
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Describe your workflow:
              </label>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Example: Every morning I check emails, write a newsletter, and post it on LinkedIn..."
                className="min-h-[120px] resize-none"
                disabled={isGenerating}
              />
            </div>
            
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-500 mb-2">Try these examples:</div>
              <div className="grid gap-2">
                {examplePrompts.map((prompt, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={(e: React.MouseEvent) => {
                      e.preventDefault();
                      setDescription(prompt);
                    }}
                    className="text-left text-xs p-2 bg-gray-50 hover:bg-gray-100 rounded border text-gray-700 transition-colors"
                    disabled={isGenerating}
                  >
                    &ldquo;{prompt}&rdquo;
                  </button>
                ))}
              </div>
            </div>
            
            <div className="flex items-center justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                disabled={isGenerating}
              >
                <Mic className="w-4 h-4" />
                Voice Input (Coming Soon)
              </Button>
              
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={isGenerating}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={!description.trim() || isGenerating}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Generate Workflow
                    </>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
