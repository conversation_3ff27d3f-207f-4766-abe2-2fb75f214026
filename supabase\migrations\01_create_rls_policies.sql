-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies
-- 1. Allow users to read their own data
CREATE POLICY "Users can view own data"
ON users
FOR SELECT
USING (auth.uid()::text = clerk_id);

-- 2. Allow users to update their own data
CREATE POLICY "Users can update own data"
ON users
FOR UPDATE
USING (auth.uid()::text = clerk_id)
WITH CHECK (auth.uid()::text = clerk_id);

-- 3. Allow insert through the Clerk webhook only
CREATE POLICY "Allow insert through webhook"
ON users
FOR INSERT
WITH CHECK (
  -- This assumes the webhook uses a service role
  -- The service role bypasses RLS, so this is more for documentation
  true
);

-- 4. Prevent manual deletion (handled by <PERSON> webhook)
CREATE POLICY "Prevent manual deletion"
ON users
FOR DELETE
USING (false);

-- Grant necessary permissions to authenticated users
GRANT SELECT, UPDATE ON users TO authenticated;

-- Note: The service role used by the Clerk webhook will bypass RLS
-- So it can perform all necessary operations regardless of these policies 