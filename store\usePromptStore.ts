import { create } from 'zustand';
import { createSupabaseClient } from '@/lib/supabase';

export interface BrandPersona {
  id: string;
  name: string;
  description: string;
  style_keywords: string[];
  color_palette: any;
  tone_of_voice: string;
  target_platforms: string[];
  industry: string;
  is_template: boolean;
}

export interface GeneratedPrompt {
  id: string;
  prompt_type: string;
  platform: string;
  prompt_text: string;
  prompt_parameters: any;
  input_data: any;
  quality_score?: number;
  is_favorited: boolean;
  created_at: string;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  persona_type: string;
  template_data: any;
  is_public: boolean;
}

interface PromptState {
  // Data
  personas: BrandPersona[];
  selectedPersona: BrandPersona | null;
  generatedPrompts: GeneratedPrompt[];
  workflowTemplates: WorkflowTemplate[];
  currentProject: any;
  
  // UI State
  isLoading: boolean;
  isGenerating: boolean;
  error: string | null;
  
  // Actions
  fetchPersonas: (userId: string) => Promise<void>;
  selectPersona: (persona: BrandPersona) => void;
  createPersona: (userId: string, persona: Partial<BrandPersona>) => Promise<void>;
  generatePrompt: (userId: string, type: string, data: any) => Promise<GeneratedPrompt>;
  savePrompt: (userId: string, prompt: Partial<GeneratedPrompt>) => Promise<void>;
  fetchPrompts: (userId: string, projectId?: string) => Promise<void>;
  fetchWorkflowTemplates: () => Promise<void>;
  createProject: (userId: string, name: string, personaId: string) => Promise<any>;
  clearError: () => void;
}

export const usePromptStore = create<PromptState>((set, get) => ({
  // Initial state
  personas: [],
  selectedPersona: null,
  generatedPrompts: [],
  workflowTemplates: [],
  currentProject: null,
  isLoading: false,
  isGenerating: false,
  error: null,

  // Actions
  fetchPersonas: async (userId: string) => {
    set({ isLoading: true, error: null });
    try {
      const supabase = createSupabaseClient(userId);
      const { data, error } = await supabase
        .from('brand_personas')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      set({ personas: data || [], isLoading: false });
    } catch (error) {
      console.error('Error fetching personas:', error);
      set({ error: 'Failed to fetch personas', isLoading: false });
    }
  },

  selectPersona: (persona: BrandPersona) => {
    set({ selectedPersona: persona });
  },

  createPersona: async (userId: string, persona: Partial<BrandPersona>) => {
    set({ isLoading: true, error: null });
    try {
      const supabase = createSupabaseClient(userId);
      const { data, error } = await supabase
        .from('brand_personas')
        .insert([{ ...persona, created_by: userId }])
        .select()
        .single();

      if (error) throw error;
      
      const currentPersonas = get().personas;
      set({ 
        personas: [data, ...currentPersonas],
        selectedPersona: data,
        isLoading: false 
      });
    } catch (error) {
      console.error('Error creating persona:', error);
      set({ error: 'Failed to create persona', isLoading: false });
    }
  },

  generatePrompt: async (userId: string, type: string, data: any): Promise<GeneratedPrompt> => {
    set({ isGenerating: true, error: null });
    try {
      // This will be implemented with the actual AI generation
      const prompt: GeneratedPrompt = {
        id: `temp-${Date.now()}`,
        prompt_type: type,
        platform: data.platform || 'instagram',
        prompt_text: 'Generated prompt will appear here...',
        prompt_parameters: data,
        input_data: data,
        is_favorited: false,
        created_at: new Date().toISOString(),
      };

      set({ isGenerating: false });
      return prompt;
    } catch (error) {
      console.error('Error generating prompt:', error);
      set({ error: 'Failed to generate prompt', isGenerating: false });
      throw error;
    }
  },

  savePrompt: async (userId: string, prompt: Partial<GeneratedPrompt>) => {
    try {
      const supabase = createSupabaseClient(userId);
      const { data, error } = await supabase
        .from('generated_prompts')
        .insert([{ ...prompt, created_by: userId }])
        .select()
        .single();

      if (error) throw error;
      
      const currentPrompts = get().generatedPrompts;
      set({ generatedPrompts: [data, ...currentPrompts] });
    } catch (error) {
      console.error('Error saving prompt:', error);
      set({ error: 'Failed to save prompt' });
    }
  },

  fetchPrompts: async (userId: string, projectId?: string) => {
    set({ isLoading: true, error: null });
    try {
      const supabase = createSupabaseClient(userId);
      let query = supabase
        .from('generated_prompts')
        .select('*')
        .order('created_at', { ascending: false });

      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      const { data, error } = await query;

      if (error) throw error;
      set({ generatedPrompts: data || [], isLoading: false });
    } catch (error) {
      console.error('Error fetching prompts:', error);
      set({ error: 'Failed to fetch prompts', isLoading: false });
    }
  },

  fetchWorkflowTemplates: async () => {
    set({ isLoading: true, error: null });
    try {
      const supabase = createSupabaseClient('anonymous'); // Public templates
      const { data, error } = await supabase
        .from('workflow_templates')
        .select('*')
        .eq('is_public', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      set({ workflowTemplates: data || [], isLoading: false });
    } catch (error) {
      console.error('Error fetching workflow templates:', error);
      set({ error: 'Failed to fetch templates', isLoading: false });
    }
  },

  createProject: async (userId: string, name: string, personaId: string) => {
    set({ isLoading: true, error: null });
    try {
      const supabase = createSupabaseClient(userId);
      const { data, error } = await supabase
        .from('projects')
        .insert([{
          name,
          persona_id: personaId,
          created_by: userId,
          canvas_data: { nodes: [], edges: [] }
        }])
        .select()
        .single();

      if (error) throw error;
      
      set({ currentProject: data, isLoading: false });
      return data;
    } catch (error) {
      console.error('Error creating project:', error);
      set({ error: 'Failed to create project', isLoading: false });
      throw error;
    }
  },

  clearError: () => {
    set({ error: null });
  },
}));
