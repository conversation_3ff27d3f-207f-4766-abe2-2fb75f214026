export interface TaskData {
  id: string;
  title: string;
  description?: string;
  minutes?: number;
  frequency?: string;
  difficulty?: number;
  painPoint?: boolean;
  type?: 'start' | 'task' | 'end';
}

export interface ToolSuggestion {
  name: string;
  description: string;
  url: string;
}

export interface WorkflowData {
  id: string;
  title: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type NodeType = 'start' | 'task' | 'end';

export interface CustomNodeData extends TaskData {
  suggestions?: ToolSuggestion[];
}
