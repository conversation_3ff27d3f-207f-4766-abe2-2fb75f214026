'use client';

import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-purple-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="max-w-7xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-purple-100 dark:bg-purple-900/50 mb-8">
            <span className="text-sm text-purple-800 dark:text-purple-200">🎨 AI-Powered Brand Magic</span>
              </div>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
            Turn Your Brand Photos Into <span className="text-purple-600 dark:text-purple-400">Killer Prompts</span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Get high-conversion, aesthetic-perfect prompts for Midjourney, ChatGPT, and Firefly — based on your product images, brand tone, and goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/create">
              <Button size="lg" className="bg-purple-600 hover:bg-purple-700">
                Try It Free
                </Button>
              </Link>
            <Button size="lg" variant="outline">
              See In Action
            </Button>
          </div>
        </div>

        {/* Visual Steps */}
        <div className="max-w-5xl mx-auto mt-20 grid grid-cols-1 sm:grid-cols-3 gap-8">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mb-4">
              <Image src="/file.svg" alt="Upload" width={24} height={24} />
            </div>
            <h3 className="font-semibold mb-2">Upload Image</h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">Drop in your product or moodboard image</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mb-4">
              <Image src="/globe.svg" alt="Goal" width={24} height={24} />
                    </div>
            <h3 className="font-semibold mb-2">Pick Goal</h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">Tell us what you want: Ad, Reels, IG Post, etc.</p>
                    </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mb-4">
              <Image src="/window.svg" alt="Prompts" width={24} height={24} />
            </div>
            <h3 className="font-semibold mb-2">Get Prompts</h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">We generate brand-quality prompts you can use instantly</p>
          </div>
        </div>
      </section>

      {/* What Makes Us Different */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl sm:text-4xl font-bold mb-16 text-center">What Makes Us Different</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl">
              <div className="flex items-start gap-4">
                <span className="text-red-500">❌</span>
                <div>
                  <h3 className="font-semibold mb-2">Others: Generic AI Tools</h3>
                  <p className="text-gray-600 dark:text-gray-300">One-size-fits-all prompts that don't understand your specific workflow needs.</p>
          </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl">
              <div className="flex items-start gap-4">
                <span className="text-green-500">✓</span>
                <div>
                  <h3 className="font-semibold mb-2">SnaPrompt: Brand-Aware AI</h3>
                  <p className="text-gray-600 dark:text-gray-300">Tailored to your brand style with image-aware prompt creation.</p>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl">
              <div className="flex items-start gap-4">
                <span className="text-green-500">✓</span>
                <div>
                  <h3 className="font-semibold mb-2">Visual Prompt Mapping</h3>
                  <p className="text-gray-600 dark:text-gray-300">See your entire prompt journey with beautiful, interactive diagrams.</p>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-12 text-center">
            <p className="text-lg font-medium text-gray-700 dark:text-gray-200 italic">
              "Prompt-only precision — for brands who care about visual identity."
            </p>
          </div>
        </div>
      </section>

      {/* Prompt Samples Gallery */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-4">Prompt Samples Gallery</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-16">See the magic in action with our sample prompts</p>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-100 dark:border-gray-700">
              <h3 className="font-semibold mb-4">Midjourney Prompt</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm italic">"Flat lay, soft beige tones, minimal aesthetic..."</p>
                  </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-100 dark:border-gray-700">
              <h3 className="font-semibold mb-4">Caption Prompt</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm italic">"Glow. Hydrate. Repeat. 🌿 The ritual your skin deserves."</p>
                  </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-100 dark:border-gray-700">
              <h3 className="font-semibold mb-4">Reels Hook Prompt</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm italic">"Soft unboxing, ASMR wipes, 3-second product reveal"</p>
                  </div>
                </div>
              </div>
      </section>

      {/* Bonus Tools */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-4">Bonus Tools & Magic Features</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-16">Extra magic to supercharge your content</p>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-100 dark:border-gray-700">
              <h3 className="font-semibold mb-2">Reverse Prompt</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Upload a competitor's ad → we break down style, tone, and recreate better prompts</p>
              </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-100 dark:border-gray-700">
              <h3 className="font-semibold mb-2">Virality Predictor</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Know if your prompt is likely to trend before you shoot</p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-100 dark:border-gray-700">
              <h3 className="font-semibold mb-2">Prompt Packs</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Valentine's Drop, Summer Essentials, Minimal Launch templates</p>
            </div>
          </div>
        </div>
      </section>

      {/* Perfect For */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-4">Perfect For</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-16">Whether you're a solo creator or leading a team, SnaPrompt adapts to your needs.</p>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <span role="img" aria-label="Skincare">🧴</span>
              </div>
              <h3 className="font-semibold mb-2">Skincare Brands</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Create serene, aesthetic product content</p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <span role="img" aria-label="Indie">✨</span>
              </div>
              <h3 className="font-semibold mb-2">Indie Creators</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Stand out with unique, branded visuals</p>
          </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <span role="img" aria-label="Photographers">📸</span>
                </div>
              <h3 className="font-semibold mb-2">Product Photographers</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Elevate your commercial photography</p>
                </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-100 dark:border-gray-700">
              <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <span role="img" aria-label="Agencies">🏢</span>
                </div>
              <h3 className="font-semibold mb-2">Creative Agencies</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Scale your content production</p>
                </div>
          </div>
        </div>
      </section>

      {/* Simple Pricing */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-4">Simple Pricing</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-16">Start free, upgrade when you're ready to scale</p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Free Plan */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-bold mb-2">Free</h3>
              <div className="text-3xl font-bold mb-6">$0<span className="text-gray-500 text-base font-normal">/mo</span></div>
              <ul className="text-left space-y-4 mb-6">
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span>10 prompts per month</span>
                  </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span>Basic templates</span>
                  </li>
                </ul>
                  <Button variant="outline" className="w-full">Start Free</Button>
            </div>
            
            {/* Creator Plan */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border-2 border-purple-500 dark:border-purple-400 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm">Most Popular</span>
              </div>
              <h3 className="text-xl font-bold mb-2">Creator</h3>
              <div className="text-3xl font-bold mb-6">$15<span className="text-gray-500 text-base font-normal">/mo</span></div>
              <ul className="text-left space-y-4 mb-6">
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span>100 prompts per month</span>
                  </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span>All templates</span>
                  </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span>Email support</span>
                  </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span>Advanced AI features</span>
                  </li>
                </ul>
              <Button className="w-full bg-purple-600 hover:bg-purple-700">Start Free Trial</Button>
            </div>

            {/* Studio Plan */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-bold mb-2">Studio</h3>
              <div className="text-3xl font-bold mb-6">$49<span className="text-gray-500 text-base font-normal">/mo</span></div>
              <ul className="text-left space-y-4 mb-6">
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span>500 prompts per month</span>
                  </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span>Team collaboration (5 seats)</span>
                  </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span>Custom brand personas</span>
                  </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span>Priority support</span>
                  </li>
                </ul>
                  <Button variant="outline" className="w-full">Contact Sales</Button>
            </div>
          </div>
        </div>
      </section>

      {/* What Our Users Say */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-16">What Our Users Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl">
              <div className="text-yellow-400 mb-4">★★★★★</div>
              <p className="text-gray-600 dark:text-gray-300 mb-4">"It feels like I have a creative director in my browser. The AI recommendations are spot-on!"</p>
              <div className="flex items-center justify-center gap-2">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/50 rounded-full"></div>
                <div>
                  <p className="font-semibold">Sarah Johnson</p>
                  <p className="text-sm text-gray-500">Skincare Brand</p>
                </div>
                  </div>
                </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl">
              <div className="text-yellow-400 mb-4">★★★★★</div>
              <p className="text-gray-600 dark:text-gray-300 mb-4">"We planned a week of shoots in 10 minutes. The prompt generation is incredible."</p>
              <div className="flex items-center justify-center gap-2">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-full"></div>
                <div>
                  <p className="font-semibold">Mike Chen</p>
                  <p className="text-sm text-gray-500">Product Photographer</p>
                </div>
                  </div>
                </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl">
              <div className="text-yellow-400 mb-4">★★★★★</div>
              <p className="text-gray-600 dark:text-gray-300 mb-4">"Our prompts finally feel on-brand. Game-changer for our agency."</p>
              <div className="flex items-center justify-center gap-2">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/50 rounded-full"></div>
                <div>
                  <p className="font-semibold">Alex Rivera</p>
                  <p className="text-sm text-gray-500">Creative Agency</p>
                </div>
                  </div>
                </div>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-6">
            Start creating brand-ready visuals — in minutes, not days.
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of creators and teams who've transformed their content with AI-powered prompt generation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/create">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
                Start Free
              </Button>
            </Link>
            <input
              type="email"
              placeholder="Enter your email"
              className="px-6 py-3 rounded-lg text-gray-900 w-full sm:w-auto"
            />
          </div>
        </div>
      </section>
    </main>
  );
}
