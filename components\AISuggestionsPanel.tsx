'use client';

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ExternalLink, Sparkles, Loader2, Lightbulb } from "lucide-react";
import { useWorkflow } from "@/store/useWorkflow";
import { AIToolSuggestion } from "@/lib/gemini";

interface AISuggestionsPanelProps {
  selectedNodeId?: string;
  selectedNodeData?: {
    title: string;
    description?: string;
    type?: string;
  };
}

export default function AISuggestionsPanel({ selectedNodeId, selectedNodeData }: AISuggestionsPanelProps) {
  const { 
    suggestions, 
    workflowOptimizations = [], 
    isLoadingSuggestions, 
    fetchAISuggestions, 
    fetchWorkflowOptimizations 
  } = useWorkflow();

  const nodeSuggestions = selectedNodeId ? suggestions[selectedNodeId] || [] : [];
  const taskDescription = selectedNodeData ? `${selectedNodeData.title}${selectedNodeData.description ? ': ' + selectedNodeData.description : ''}` : '';

  const handleGetSuggestions = () => {
    if (selectedNodeId && taskDescription) {
      fetchAISuggestions(selectedNodeId, taskDescription);
    }
  };

  const handleGetWorkflowOptimizations = () => {
    fetchWorkflowOptimizations();
  };

  const openTool = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  if (!selectedNodeId || !selectedNodeData || selectedNodeData.type !== 'task') {
    return (
      <div className="w-80 p-4 space-y-4 border-l border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm">
        <Card className="border-purple-200 dark:border-purple-800 bg-white dark:bg-gray-800">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700 dark:text-purple-400 text-lg">
              <Sparkles className="w-5 h-5" />
              AI Assistant
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 dark:text-gray-300 text-center py-4">
              Select a task node to get AI-powered tool suggestions and workflow optimizations
            </p>
          </CardContent>
        </Card>

        {/* Workflow Optimizations */}
        <Card className="border-blue-200 dark:border-blue-800 bg-white dark:bg-gray-800">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-400 text-lg">
              <Lightbulb className="w-5 h-5" />
              Workflow Optimizations
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!isLoadingSuggestions && workflowOptimizations.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  Get AI suggestions to optimize your workflow
                </p>
                <Button 
                  onClick={handleGetWorkflowOptimizations}
                  variant="outline"
                  className="border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/50"
                >
                  <Lightbulb className="w-4 h-4 mr-2" />
                  Get Optimizations
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                {isLoadingSuggestions ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="w-6 h-6 animate-spin text-blue-600 dark:text-blue-400" />
                  </div>
                ) : (
                  <>
                    {workflowOptimizations.map((optimization, index) => (
                      <div key={index} className="p-2 bg-blue-50 dark:bg-blue-900/50 rounded-md border border-blue-200 dark:border-blue-800">
                        <p className="text-sm text-blue-800 dark:text-blue-200">{optimization}</p>
                      </div>
                    ))}
                    <Button 
                      onClick={handleGetWorkflowOptimizations}
                      variant="outline"
                      size="sm"
                      className="w-full mt-2 border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/50"
                    >
                      <Lightbulb className="w-4 h-4 mr-2" />
                      Refresh Optimizations
                    </Button>
                  </>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-80 p-4 space-y-4 border-l border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm">
      {/* Task-specific AI Suggestions */}
      <Card className="border-purple-200 dark:border-purple-800 bg-white dark:bg-gray-800">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-purple-700 dark:text-purple-400 text-lg">
            <Sparkles className="w-5 h-5" />
            AI Tool Suggestions
          </CardTitle>
          <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
            For: <span className="font-medium">{selectedNodeData.title}</span>
          </p>
        </CardHeader>
        <CardContent className="space-y-3">
          {!isLoadingSuggestions && nodeSuggestions.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                Get AI-powered tool recommendations for this task
              </p>
              <Button 
                onClick={handleGetSuggestions}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Get AI Suggestions
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {isLoadingSuggestions ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="w-6 h-6 animate-spin text-purple-600 dark:text-purple-400" />
                </div>
              ) : (
                <>
                  {nodeSuggestions.map((suggestion: AIToolSuggestion, index: number) => (
                    <TooltipProvider key={index}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div 
                            className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                            onClick={() => openTool(suggestion.url)}
                          >
                            <div className="flex items-start justify-between gap-2">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="font-semibold text-sm text-gray-900 dark:text-gray-100">
                                    {suggestion.name}
                                  </h4>
                                  <Badge variant="secondary" className="text-xs">
                                    {suggestion.category}
                                  </Badge>
                                </div>
                                <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-2">
                                  {suggestion.description}
                                </p>
                              </div>
                              <ExternalLink className="w-4 h-4 text-gray-400 flex-shrink-0" />
                            </div>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Click to open {suggestion.name}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))}
                  
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleGetSuggestions}
                    className="w-full mt-2"
                  >
                    <Sparkles className="w-4 h-4 mr-2" />
                    Refresh Suggestions
                  </Button>
                </>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
