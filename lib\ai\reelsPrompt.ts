export interface ReelsConfig {
  persona: any;
  platform: string;
  mood: string;
  description: string;
  duration?: string;
  style?: string;
}

export function generateReelsPrompt(config: ReelsConfig): string {
  const { persona, platform, mood, description, duration = '30 seconds', style = 'engaging' } = config;

  const platformSpecifics = {
    instagram: {
      format: 'Instagram Reels (9:16 vertical)',
      features: 'trending audio, quick cuts, text overlays',
      engagement: 'hooks within first 3 seconds, trending hashtags'
    },
    tiktok: {
      format: 'TikTok video (9:16 vertical)', 
      features: 'trending sounds, effects, transitions',
      engagement: 'viral hooks, trending challenges, duets'
    },
    youtube: {
      format: 'YouTube Shorts (9:16 vertical)',
      features: 'clear audio, good lighting, engaging thumbnails',
      engagement: 'strong opening, clear value proposition'
    },
    facebook: {
      format: 'Facebook Reels (9:16 vertical)',
      features: 'captions, clear visuals, brand consistency',
      engagement: 'community-focused content, shareability'
    }
  };

  const currentPlatform = platformSpecifics[platform as keyof typeof platformSpecifics] || platformSpecifics.instagram;

  return `Create a ${duration} ${style} ${currentPlatform.format} script for ${persona.name || 'the brand'}.

**Content Brief:**
${description}

**Brand Voice:** ${persona.description || 'Professional and engaging'}
**Mood/Tone:** ${mood}
**Platform:** ${platform.charAt(0).toUpperCase() + platform.slice(1)}

**Script Structure:**

**HOOK (0-3 seconds):**
- Create an attention-grabbing opening that stops the scroll
- Use pattern interrupts, surprising statements, or visual hooks
- Make viewers want to keep watching

**MAIN CONTENT (3-${parseInt(duration) - 5} seconds):**
- Deliver the core message clearly and concisely
- Use ${currentPlatform.features}
- Maintain ${mood} energy throughout
- Include brand personality elements

**CALL-TO-ACTION (Last 5 seconds):**
- Clear, specific action for viewers to take
- Align with brand goals and ${platform} best practices
- ${currentPlatform.engagement}

**Technical Requirements:**
- Format: ${currentPlatform.format}
- Duration: ${duration}
- Style: ${style}
- Platform optimization: ${currentPlatform.engagement}

**Additional Notes:**
- Include suggested visual cues and transitions
- Mention any trending elements or sounds to consider
- Ensure content aligns with ${persona.name || 'brand'} values and messaging
- Optimize for ${platform} algorithm and user behavior

Generate a complete, ready-to-film script with timing markers and production notes.`;
}

export function generateReelsWithGemini(config: ReelsConfig): Promise<string> {
  const prompt = generateReelsPrompt(config);
  
  // This will be replaced with actual Gemini API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(`🎬 ${config.platform.toUpperCase()} REELS SCRIPT

**HOOK (0-3s):**
"Wait... did you know this about [topic]?"
[Visual: Close-up shot with dramatic lighting]

**MAIN CONTENT (3-25s):**
[Scene 1] Quick transition showing the problem
[Scene 2] Reveal the solution with ${config.mood} energy
[Scene 3] Show the transformation/result

**CTA (25-30s):**
"Follow for more tips like this! 👆"
[Visual: Clear brand logo/handle display]

**Production Notes:**
- Use trending audio from ${config.platform}
- Add text overlays for key points
- Include ${config.mood} color scheme
- Film in good natural lighting`);
    }, 2000);
  });
}
