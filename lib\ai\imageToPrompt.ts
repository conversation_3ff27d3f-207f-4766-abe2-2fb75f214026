import { GoogleGenAI } from "@google/genai";

if (!process.env.NEXT_PUBLIC_GEMINI_API_KEY && !process.env.GEMINI_API_KEY) {
  console.error("Gemini API key is missing. Please set the NEXT_PUBLIC_GEMINI_API_KEY or GEMINI_API_KEY environment variable.");
}

const ai = new GoogleGenAI({ 
  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY || "" 
});

const model = "gemini-2.5-flash";

export interface PromptGenerationOptions {
  persona: string;
  platform: string;
  style?: string;
  aspectRatio?: string;
  cameraSettings?: string;
  mood?: string;
  additionalElements?: string[];
}

export async function generateVisualPrompt(
  base64Image: string, 
  mimeType: string, 
  options: PromptGenerationOptions
): Promise<string> {
  const imagePart = {
    inlineData: { mimeType, data: base64Image },
  };

  const textPart = {
    text: `You're a top-tier AI prompt engineer and creative director specializing in ${options.platform} content.

Analyze this reference image and generate a detailed visual prompt for ${options.platform} that matches the "${options.persona}" brand persona.

Consider these requirements:
- Brand persona: ${options.persona}
- Platform: ${options.platform}
- Style: ${options.style || 'brand-appropriate'}
- Aspect ratio: ${options.aspectRatio || '4:5'}
- Camera settings: ${options.cameraSettings || 'professional photography'}
- Mood: ${options.mood || 'brand-aligned'}
${options.additionalElements?.length ? `- Additional elements: ${options.additionalElements.join(', ')}` : ''}

Generate a comprehensive prompt that includes:
1. Subject and composition details
2. Lighting and mood
3. Color palette and style
4. Camera settings and technical details
5. Platform-specific optimizations

Output ONLY the final prompt string, no explanations or formatting.`,
  };

  try {
    const response = await ai.models.generateContent({
      model,
      contents: { parts: [imagePart, textPart] },
    });

    return response.text.trim();
  } catch (error) {
    console.error("Error generating visual prompt:", error);
    throw new Error("Failed to generate visual prompt. Please try again.");
  }
}

export async function generatePromptFromDescription(
  description: string,
  options: PromptGenerationOptions
): Promise<string> {
  const prompt = `You're a top-tier AI prompt engineer and creative director.

Create a detailed visual prompt for ${options.platform} based on this description: "${description}"

Brand persona: ${options.persona}
Platform: ${options.platform}
Style: ${options.style || 'brand-appropriate'}
Aspect ratio: ${options.aspectRatio || '4:5'}
Camera settings: ${options.cameraSettings || 'professional photography'}
Mood: ${options.mood || 'brand-aligned'}
${options.additionalElements?.length ? `Additional elements: ${options.additionalElements.join(', ')}` : ''}

Generate a comprehensive prompt that includes:
1. Subject and composition details
2. Lighting and mood
3. Color palette and style
4. Camera settings and technical details
5. Platform-specific optimizations

Output ONLY the final prompt string, no explanations or formatting.`;

  try {
    const response = await ai.models.generateContent({
      model,
      contents: [{ text: prompt }],
    });

    return response.text.trim();
  } catch (error) {
    console.error("Error generating prompt from description:", error);
    throw new Error("Failed to generate prompt. Please try again.");
  }
}

export async function improvePrompt(rawPrompt: string, platform: string = "midjourney"): Promise<string> {
  const prompt = `You are an expert prompt engineer for ${platform} and other AI image generation tools.

Here is a raw prompt:  
"${rawPrompt}"

Improve it by:
- Making it more descriptive and specific
- Adding appropriate camera details and technical specifications
- Enhancing realism or aesthetic appeal
- Optimizing for ${platform} best practices
- Adding relevant style parameters and aspect ratios
- Ensuring clarity and precision

Return only the improved prompt, no explanations.`;

  try {
    const response = await ai.models.generateContent({
      model,
      contents: [{ text: prompt }],
    });

    return response.text.trim();
  } catch (error) {
    console.error("Error improving prompt:", error);
    throw new Error("Failed to improve prompt. Please try again.");
  }
}

export async function generateVariations(basePrompt: string, count: number = 3): Promise<string[]> {
  const prompt = `You are an expert prompt engineer. 

Take this base prompt: "${basePrompt}"

Generate ${count} creative variations that:
- Maintain the core concept and style
- Explore different angles, compositions, or moods
- Add unique creative elements
- Keep the same quality and detail level

Return each variation on a new line, numbered 1-${count}.`;

  try {
    const response = await ai.models.generateContent({
      model,
      contents: [{ text: prompt }],
    });

    const variations = response.text.trim()
      .split('\n')
      .map(line => line.replace(/^\d+\.\s*/, '').trim())
      .filter(line => line.length > 0);

    return variations.slice(0, count);
  } catch (error) {
    console.error("Error generating variations:", error);
    throw new Error("Failed to generate variations. Please try again.");
  }
}
