export interface VisualPromptConfig {
  persona: any;
  platform: string;
  aspectRatio: string;
  cameraStyle: string;
  mood: string;
  description: string;
  uploadedImage?: string | null;
  style?: string;
  lighting?: string;
}

export function generateVisualPrompt(config: VisualPromptConfig): string {
  const { 
    persona, 
    platform, 
    aspectRatio, 
    cameraStyle, 
    mood, 
    description, 
    uploadedImage,
    style = 'modern',
    lighting = 'natural'
  } = config;

  const platformSpecifics = {
    instagram: {
      optimalSizes: '1080x1080 (square), 1080x1350 (portrait), 1080x608 (landscape)',
      style: 'aesthetic, lifestyle-focused, high-quality',
      trends: 'minimalist, authentic moments, behind-the-scenes'
    },
    facebook: {
      optimalSizes: '1200x630 (shared), 1080x1080 (square), 1200x675 (cover)',
      style: 'engaging, community-focused, shareable',
      trends: 'authentic content, user-generated, storytelling'
    },
    linkedin: {
      optimalSizes: '1200x627 (shared), 1080x1080 (square), 1584x396 (cover)',
      style: 'professional, clean, business-appropriate',
      trends: 'thought leadership, industry insights, team culture'
    },
    tiktok: {
      optimalSizes: '1080x1920 (vertical), 1080x1080 (square)',
      style: 'dynamic, authentic, trend-focused',
      trends: 'vertical video, trending effects, user-generated'
    },
    youtube: {
      optimalSizes: '1280x720 (16:9), 1920x1080 (HD), 1080x1920 (shorts)',
      style: 'cinematic, engaging thumbnails, clear visuals',
      trends: 'high production value, storytelling, educational'
    }
  };

  const currentPlatform = platformSpecifics[platform as keyof typeof platformSpecifics] || platformSpecifics.instagram;

  const aspectRatioGuide = {
    '1:1': 'Square format - perfect for Instagram feed, Facebook posts',
    '4:5': 'Portrait format - optimal for Instagram feed engagement',
    '16:9': 'Landscape format - ideal for YouTube, LinkedIn, Facebook covers',
    '9:16': 'Vertical format - perfect for Stories, Reels, TikTok',
    '3:4': 'Portrait format - great for Pinterest, Instagram Stories'
  };

  const cameraStyleGuide = {
    professional: 'studio lighting, crisp focus, commercial quality',
    lifestyle: 'natural settings, candid moments, authentic feel',
    minimalist: 'clean backgrounds, simple composition, negative space',
    cinematic: 'dramatic lighting, depth of field, movie-like quality',
    documentary: 'real-world settings, natural lighting, storytelling'
  };

  return `Create a stunning ${platform} visual for ${persona.name || 'the brand'}.

**Content Brief:**
${description}

**Brand Identity:**
- Brand: ${persona.name || 'Brand Name'}
- Voice: ${persona.description || 'Professional and engaging'}
- Mood: ${mood}
- Style: ${style}

**Technical Specifications:**
- Platform: ${platform.charAt(0).toUpperCase() + platform.slice(1)}
- Aspect Ratio: ${aspectRatio} (${aspectRatioGuide[aspectRatio as keyof typeof aspectRatioGuide] || 'Custom format'})
- Optimal sizes: ${currentPlatform.optimalSizes}
- Camera Style: ${cameraStyle} (${cameraStyleGuide[cameraStyle as keyof typeof cameraStyleGuide] || 'Custom style'})
- Lighting: ${lighting}

**Visual Composition:**

**MAIN SUBJECT:**
- Primary focus: ${description}
- Brand elements integration
- ${mood} aesthetic throughout
- ${cameraStyle} photography approach

**BACKGROUND & SETTING:**
- Environment that supports the message
- ${style} design elements
- Platform-appropriate styling: ${currentPlatform.style}
- Color palette matching ${mood} mood

**LIGHTING & ATMOSPHERE:**
- ${lighting} lighting setup
- Mood enhancement through lighting
- Professional quality illumination
- Platform optimization for ${platform}

**COMPOSITION RULES:**
- Rule of thirds application
- ${aspectRatio} format optimization
- Visual hierarchy and flow
- Brand consistency elements

**COLOR PALETTE:**
- Primary colors reflecting ${mood}
- Brand color integration
- Platform trend alignment: ${currentPlatform.trends}
- Accessibility considerations

**TEXT & GRAPHICS:**
- Minimal, impactful text overlay
- Brand logo placement
- Platform-specific text sizing
- Readability across devices

${uploadedImage ? `**REFERENCE IMAGE ANALYSIS:**
- Uploaded reference image provided
- Maintain similar composition/style
- Adapt elements for ${platform} optimization
- Enhance quality while preserving essence` : ''}

**PLATFORM OPTIMIZATION:**
- ${platform} best practices: ${currentPlatform.style}
- Current trends: ${currentPlatform.trends}
- Algorithm-friendly elements
- Engagement-driving visuals

**BRAND CONSISTENCY:**
- Align with ${persona.name || 'brand'} visual identity
- Consistent with previous content
- Professional quality standards
- Memorable brand elements

**FINAL OUTPUT:**
Generate a detailed visual prompt that a professional photographer or AI image generator can use to create the perfect ${platform} visual that drives engagement and represents the brand beautifully.`;
}

export function generateVisualWithGemini(config: VisualPromptConfig): Promise<string> {
  const prompt = generateVisualPrompt(config);
  
  // This will be replaced with actual Gemini API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(`📸 PROFESSIONAL ${config.platform.toUpperCase()} VISUAL PROMPT

**SCENE DESCRIPTION:**
${config.cameraStyle} photography of ${config.description}, captured in ${config.aspectRatio} format with ${config.mood} aesthetic. 

**COMPOSITION:**
- Main subject positioned using rule of thirds
- ${config.lighting} lighting creating depth and dimension
- Clean, ${config.style || 'modern'} background that doesn't compete
- Brand elements subtly integrated

**TECHNICAL DETAILS:**
- Camera: Professional DSLR with 85mm lens
- Aperture: f/2.8 for optimal depth of field
- Lighting: ${config.lighting} light with soft shadows
- Color grading: ${config.mood} tones with brand color accents

**STYLING NOTES:**
- Props: Minimal, purposeful elements
- Colors: ${config.mood} palette with brand consistency
- Mood: ${config.mood} and engaging
- Platform: Optimized for ${config.platform} engagement

**POST-PRODUCTION:**
- Professional color correction
- Brand watermark in bottom right
- ${config.aspectRatio} crop for ${config.platform}
- High resolution for quality display

This visual will perfectly represent ${config.persona.name || 'your brand'} while driving maximum engagement on ${config.platform}.`);
    }, 2000);
  });
}
