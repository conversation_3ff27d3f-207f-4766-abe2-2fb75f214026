'use client';

import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Sparkles,
  Image as ImageIcon,
  Video,
  Type,
  Target,
  Palette,
  Camera,
  Settings,
  Upload,
  Wand2,
  Copy,
  Heart,
  Download,
  Workflow
} from 'lucide-react';
import { usePromptStore } from '@/store/usePromptStore';
import PersonaSelector from './PersonaSelector';
import ImageUpload from './ImageUpload';

const PLATFORMS = [
  { value: 'instagram', label: 'Instagram', icon: '📸' },
  { value: 'tiktok', label: 'TikTok', icon: '🎵' },
  { value: 'pinterest', label: 'Pinterest', icon: '📌' },
  { value: 'facebook', label: 'Facebook', icon: '👥' },
  { value: 'linkedin', label: 'LinkedIn', icon: '💼' },
  { value: 'youtube', label: 'YouTube', icon: '📺' },
];

const PROMPT_TYPES = [
  { value: 'visual', label: 'Visual Prompt', icon: ImageIcon, description: 'Generate image prompts for AI tools' },
  { value: 'reels', label: 'Reels Script', icon: Video, description: 'Create engaging video scripts' },
  { value: 'caption', label: 'Caption', icon: Type, description: 'Write compelling captions' },
  { value: 'ad', label: 'Ad Copy', icon: Target, description: 'Generate advertising copy' },
];

const ASPECT_RATIOS = [
  { value: '1:1', label: 'Square (1:1)' },
  { value: '4:5', label: 'Portrait (4:5)' },
  { value: '16:9', label: 'Landscape (16:9)' },
  { value: '9:16', label: 'Stories (9:16)' },
];

const CAMERA_STYLES = [
  { value: 'professional', label: 'Professional Photography' },
  { value: 'lifestyle', label: 'Lifestyle Photography' },
  { value: 'product', label: 'Product Photography' },
  { value: 'portrait', label: 'Portrait Photography' },
  { value: 'macro', label: 'Macro Photography' },
  { value: 'aerial', label: 'Aerial/Drone Photography' },
];

interface PromptWorkspaceProps {
  onSwitchToWorkflow?: () => void;
}

export default function PromptWorkspace({ onSwitchToWorkflow }: PromptWorkspaceProps) {
  const { user } = useUser();
  const {
    selectedPersona,
    isGenerating,
    error,
    fetchPersonas,
    clearError
  } = usePromptStore();

  const [selectedPlatform, setSelectedPlatform] = useState('instagram');
  const [aspectRatio, setAspectRatio] = useState('4:5');
  const [cameraStyle, setCameraStyle] = useState('professional');
  const [mood, setMood] = useState('');
  const [description, setDescription] = useState('');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [selectedPromptTypes, setSelectedPromptTypes] = useState<string[]>(['visual']);
  const [includeReels, setIncludeReels] = useState(false);
  const [includeCaption, setIncludeCaption] = useState(false);
  const [includeAdCopy, setIncludeAdCopy] = useState(false);

  useEffect(() => {
    if (user?.id) {
      fetchPersonas(user.id);
    }
  }, [user?.id, fetchPersonas]);

  const handleCreateWorkflow = async () => {
    if (!selectedPersona || !user?.id || !description.trim()) return;

    try {
      // Create workflow configuration based on user selections
      const workflowConfig = {
        persona: selectedPersona,
        platform: selectedPlatform,
        aspectRatio,
        cameraStyle,
        mood,
        description,
        uploadedImage,
        promptTypes: selectedPromptTypes,
        includeReels,
        includeCaption,
        includeAdCopy
      };

      // Store workflow config and switch to workflow builder
      localStorage.setItem('pendingWorkflow', JSON.stringify(workflowConfig));

      // Trigger workflow creation event
      window.dispatchEvent(new CustomEvent('createWorkflow', { detail: workflowConfig }));

      // Switch to workflow builder after a short delay
      setTimeout(() => {
        onSwitchToWorkflow?.();
      }, 500);

    } catch (error) {
      console.error('Error creating workflow:', error);
    }
  };

  const togglePromptType = (type: string) => {
    if (type === 'visual') return; // Visual is always included

    switch (type) {
      case 'reels':
        setIncludeReels(!includeReels);
        break;
      case 'caption':
        setIncludeCaption(!includeCaption);
        break;
      case 'ad':
        setIncludeAdCopy(!includeAdCopy);
        break;
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // Add toast notification here
  };

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-600">{error}</p>
            <Button onClick={clearError} className="mt-2">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="p-4 bg-white border-b">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-gray-900">AI Prompt Studio</h1>
            <p className="text-xs text-gray-600">Configure your workflow settings</p>
          </div>
        </div>

        {/* Persona Selector */}
        <PersonaSelector />
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-4 space-y-4">
          {/* Workflow Configuration */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-base">
                <Settings className="w-4 h-4" />
                Workflow Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Prompt Types Selection */}
              <div>
                <label className="text-sm font-medium mb-2 block">Include in Workflow</label>
                <div className="grid grid-cols-2 gap-2">
                  {PROMPT_TYPES.map((type) => (
                    <div
                      key={type.value}
                      className={`p-3 border rounded-lg cursor-pointer transition-all ${
                        type.value === 'visual' ||
                        (type.value === 'reels' && includeReels) ||
                        (type.value === 'caption' && includeCaption) ||
                        (type.value === 'ad' && includeAdCopy)
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => togglePromptType(type.value)}
                    >
                      <div className="flex items-center gap-2">
                        <type.icon className="w-4 h-4" />
                        <span className="text-sm font-medium">{type.label}</span>
                        {type.value === 'visual' && (
                          <Badge variant="secondary" className="text-xs">Required</Badge>
                        )}
                      </div>
                      <p className="text-xs text-gray-600 mt-1">{type.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Configuration Options */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Platform Selection */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Platform</label>
                  <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {PLATFORMS.map((platform) => (
                        <SelectItem key={platform.value} value={platform.value}>
                          <span className="flex items-center gap-2">
                            <span>{platform.icon}</span>
                            {platform.label}
                          </span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Aspect Ratio */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Aspect Ratio</label>
                  <Select value={aspectRatio} onValueChange={setAspectRatio}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {ASPECT_RATIOS.map((ratio) => (
                        <SelectItem key={ratio.value} value={ratio.value}>
                          {ratio.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Camera Style */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Camera Style</label>
                  <Select value={cameraStyle} onValueChange={setCameraStyle}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {CAMERA_STYLES.map((style) => (
                        <SelectItem key={style.value} value={style.value}>
                          {style.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Mood */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Mood</label>
                  <Input
                    placeholder="e.g., bright, moody, minimalist"
                    value={mood}
                    onChange={(e) => setMood(e.target.value)}
                  />
                </div>
              </div>

              {/* Image Upload */}
              <div>
                <label className="text-sm font-medium mb-2 block">Reference Image (Optional)</label>
                <ImageUpload onImageUpload={setUploadedImage} />
              </div>

              {/* Description */}
              <div>
                <label className="text-sm font-medium mb-2 block">Project Description</label>
                <Textarea
                  placeholder="Describe what you want to create..."
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Create Workflow Button */}
          <Button
            onClick={handleCreateWorkflow}
            disabled={!selectedPersona || !description.trim() || isGenerating}
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-3"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Sparkles className="w-5 h-5 mr-2 animate-spin" />
                Creating Workflow...
              </>
            ) : (
              <>
                <Wand2 className="w-5 h-5 mr-2" />
                Create Workflow
              </>
            )}
          </Button>

          {/* Workflow Preview */}
          <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Workflow className="w-4 h-4" />
                Workflow Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span>Brand Persona</span>
                </div>
                <span>→</span>
                {uploadedImage && (
                  <>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>Image Analysis</span>
                    </div>
                    <span>→</span>
                  </>
                )}
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                  <span>Visual Prompt</span>
                </div>
                {(includeReels || includeCaption || includeAdCopy) && (
                  <>
                    <span>→</span>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Content Generation</span>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
