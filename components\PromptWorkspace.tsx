'use client';

import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Sparkles, 
  Image as ImageIcon, 
  Video, 
  Type, 
  Target,
  Palette,
  Camera,
  Settings,
  Upload,
  Wand2,
  Copy,
  Heart,
  Download
} from 'lucide-react';
import { usePromptStore } from '@/store/usePromptStore';
import PersonaSelector from './PersonaSelector';
import ImageUpload from './ImageUpload';

const PLATFORMS = [
  { value: 'instagram', label: 'Instagram', icon: '📸' },
  { value: 'tiktok', label: 'TikTok', icon: '🎵' },
  { value: 'pinterest', label: 'Pinterest', icon: '📌' },
  { value: 'facebook', label: 'Facebook', icon: '👥' },
  { value: 'linkedin', label: 'LinkedIn', icon: '💼' },
  { value: 'youtube', label: 'YouTube', icon: '📺' },
];

const PROMPT_TYPES = [
  { value: 'visual', label: 'Visual Prompt', icon: ImageIcon, description: 'Generate image prompts for AI tools' },
  { value: 'reels', label: 'Reels Script', icon: Video, description: 'Create engaging video scripts' },
  { value: 'caption', label: 'Caption', icon: Type, description: 'Write compelling captions' },
  { value: 'ad', label: 'Ad Copy', icon: Target, description: 'Generate advertising copy' },
];

const ASPECT_RATIOS = [
  { value: '1:1', label: 'Square (1:1)' },
  { value: '4:5', label: 'Portrait (4:5)' },
  { value: '16:9', label: 'Landscape (16:9)' },
  { value: '9:16', label: 'Stories (9:16)' },
];

const CAMERA_STYLES = [
  { value: 'professional', label: 'Professional Photography' },
  { value: 'lifestyle', label: 'Lifestyle Photography' },
  { value: 'product', label: 'Product Photography' },
  { value: 'portrait', label: 'Portrait Photography' },
  { value: 'macro', label: 'Macro Photography' },
  { value: 'aerial', label: 'Aerial/Drone Photography' },
];

export default function PromptWorkspace() {
  const { user } = useUser();
  const {
    selectedPersona,
    isGenerating,
    error,
    fetchPersonas,
    clearError
  } = usePromptStore();

  const [activeTab, setActiveTab] = useState('visual');
  const [selectedPlatform, setSelectedPlatform] = useState('instagram');
  const [aspectRatio, setAspectRatio] = useState('4:5');
  const [cameraStyle, setCameraStyle] = useState('professional');
  const [mood, setMood] = useState('');
  const [description, setDescription] = useState('');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [generatedPrompt, setGeneratedPrompt] = useState('');

  useEffect(() => {
    if (user?.id) {
      fetchPersonas(user.id);
    }
  }, [user?.id, fetchPersonas]);

  const handleGeneratePrompt = async () => {
    if (!selectedPersona || !user?.id) return;

    try {
      // This will be implemented with actual AI generation
      setGeneratedPrompt('Your generated prompt will appear here...');
    } catch (error) {
      console.error('Error generating prompt:', error);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // Add toast notification here
  };

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-600">{error}</p>
            <Button onClick={clearError} className="mt-2">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="p-6 bg-white border-b">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900">AI Prompt Studio</h1>
            <p className="text-sm text-gray-600">Generate brand-perfect prompts with AI</p>
          </div>
        </div>

        {/* Persona Selector */}
        <PersonaSelector />
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-6 space-y-6">
          {/* Prompt Type Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="w-5 h-5" />
                Choose Prompt Type
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-4">
                  {PROMPT_TYPES.map((type) => (
                    <TabsTrigger key={type.value} value={type.value} className="flex items-center gap-2">
                      <type.icon className="w-4 h-4" />
                      {type.label}
                    </TabsTrigger>
                  ))}
                </TabsList>

                <TabsContent value="visual" className="space-y-4 mt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Platform Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Platform</label>
                      <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {PLATFORMS.map((platform) => (
                            <SelectItem key={platform.value} value={platform.value}>
                              <span className="flex items-center gap-2">
                                <span>{platform.icon}</span>
                                {platform.label}
                              </span>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Aspect Ratio */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Aspect Ratio</label>
                      <Select value={aspectRatio} onValueChange={setAspectRatio}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {ASPECT_RATIOS.map((ratio) => (
                            <SelectItem key={ratio.value} value={ratio.value}>
                              {ratio.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Camera Style */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Camera Style</label>
                      <Select value={cameraStyle} onValueChange={setCameraStyle}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {CAMERA_STYLES.map((style) => (
                            <SelectItem key={style.value} value={style.value}>
                              {style.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Mood */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Mood</label>
                      <Input
                        placeholder="e.g., bright, moody, minimalist"
                        value={mood}
                        onChange={(e) => setMood(e.target.value)}
                      />
                    </div>
                  </div>

                  {/* Image Upload */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Reference Image (Optional)</label>
                    <ImageUpload onImageUpload={setUploadedImage} />
                  </div>

                  {/* Description */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Description</label>
                    <Textarea
                      placeholder="Describe what you want to create..."
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      rows={3}
                    />
                  </div>
                </TabsContent>

                {/* Other tab contents will be similar */}
                <TabsContent value="reels" className="space-y-4 mt-6">
                  <p className="text-gray-600">Reels script generation coming soon...</p>
                </TabsContent>

                <TabsContent value="caption" className="space-y-4 mt-6">
                  <p className="text-gray-600">Caption generation coming soon...</p>
                </TabsContent>

                <TabsContent value="ad" className="space-y-4 mt-6">
                  <p className="text-gray-600">Ad copy generation coming soon...</p>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Generate Button */}
          <Button
            onClick={handleGeneratePrompt}
            disabled={!selectedPersona || isGenerating}
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-3"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Sparkles className="w-5 h-5 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Wand2 className="w-5 h-5 mr-2" />
                Generate Prompt
              </>
            )}
          </Button>

          {/* Generated Prompt */}
          {generatedPrompt && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Generated Prompt</span>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(generatedPrompt)}
                    >
                      <Copy className="w-4 h-4 mr-1" />
                      Copy
                    </Button>
                    <Button variant="outline" size="sm">
                      <Heart className="w-4 h-4 mr-1" />
                      Save
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-800 leading-relaxed">{generatedPrompt}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
