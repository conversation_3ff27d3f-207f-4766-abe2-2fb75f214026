import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { useWorkflow } from '@/store/useWorkflow'
import { useRouter } from 'next/navigation'
import { AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useUser } from '@clerk/nextjs'

interface SaveWorkflowDialogProps {
  isOpen: boolean
  onClose: () => void
}

export default function SaveWorkflowDialog({ isOpen, onClose }: SaveWorkflowDialogProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { saveWorkflow } = useWorkflow()
  const router = useRouter()
  const { user } = useUser()

  const handleSave = async () => {
    if (!name.trim()) return
    if (!user?.id) {
      setError('You must be signed in to save a workflow')
      return
    }
    
    setError(null)
    setIsSaving(true)
    
    try {
      await saveWorkflow(user.id, name.trim(), description.trim() || undefined)
      onClose()
      router.push('/projects')
    } catch (error) {
      console.error('Failed to save workflow:', error)
      setError('Failed to save workflow. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleClose = () => {
    setError(null)
    setName('')
    setDescription('')
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Save Workflow</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium">
              Name
            </label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter workflow name"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">
              Description (optional)
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter workflow description"
              rows={3}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!name.trim() || isSaving || !user}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 