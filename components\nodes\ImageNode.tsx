'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Image as ImageIcon, 
  Upload, 
  Camera,
  Edit3,
  Check,
  X,
  FileImage
} from 'lucide-react';

interface ImageNodeData {
  title: string;
  description?: string;
  type: 'image';
  config?: {
    acceptedTypes?: string[];
    maxSize?: number;
  };
  uploadedImage?: string;
}

export default function ImageNode({ data, selected, id }: NodeProps<ImageNodeData>) {
  const [isEditing, setIsEditing] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(data.uploadedImage || null);

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setUploadedImage(result);
        // Update node data
        data.uploadedImage = result;
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setUploadedImage(null);
    data.uploadedImage = undefined;
  };

  const getStatusColor = () => {
    if (uploadedImage) return 'bg-green-500';
    return 'bg-gray-400';
  };

  return (
    <div className="relative workflow-node group">
      <Handle
        type="target"
        position={Position.Left}
        id="image-input"
        className="w-3 h-3 !bg-blue-500 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="image-output"
        className="w-3 h-3 !bg-blue-500 border-2 border-white"
      />
      
      <Card
        className={`w-72 cursor-pointer transition-all duration-200 ${
          selected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-md hover:shadow-lg'
        }`}
        onDoubleClick={handleDoubleClick}
      >
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center">
              <ImageIcon className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="text-lg font-semibold">{data.title}</span>
                <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
              </div>
              <p className="text-sm text-gray-600 font-normal">{data.description}</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setIsEditing(true);
              }}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <Edit3 className="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-3">
          {uploadedImage ? (
            <div className="space-y-3">
              <div className="relative">
                <img
                  src={uploadedImage}
                  alt="Uploaded reference"
                  className="w-full h-32 object-cover rounded-lg"
                />
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeImage();
                  }}
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
              
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <FileImage className="w-3 h-3" />
                  Image uploaded
                </Badge>
                <Badge variant="outline">Ready for analysis</Badge>
              </div>
            </div>
          ) : (
            <div className="text-center py-6">
              <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <ImageIcon className="w-6 h-6 text-gray-400" />
              </div>
              <p className="text-sm text-gray-600 mb-3">No image uploaded</p>
              
              <div className="flex flex-col gap-2">
                <Button 
                  size="sm" 
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsEditing(true);
                  }}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Image
                </Button>
                
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsEditing(true);
                  }}
                >
                  <Camera className="w-4 h-4 mr-2" />
                  Take Photo
                </Button>
              </div>
            </div>
          )}

          {data.config && (
            <div className="border-t pt-3">
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <span>Max size: {data.config.maxSize || 5}MB</span>
                <span>•</span>
                <span>
                  {data.config.acceptedTypes?.map(type => 
                    type.split('/')[1].toUpperCase()
                  ).join(', ') || 'JPEG, PNG'}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Modal/Overlay */}
      {isEditing && (
        <div className="absolute inset-0 bg-white border-2 border-blue-500 rounded-lg shadow-xl z-10 p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Upload Image</h3>
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsEditing(false)}
              >
                <Check className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsEditing(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="space-y-3">
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="w-full text-sm"
            />
            
            <div className="text-xs text-gray-600">
              <p>Upload a reference image for AI analysis</p>
              <p>Supported: JPEG, PNG, WebP (max {data.config?.maxSize || 5}MB)</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
