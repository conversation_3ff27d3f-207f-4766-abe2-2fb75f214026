import React, { useState, useEffect } from 'react';
import React<PERSON><PERSON>, {
  Background,
  Controls,
  MiniMap,
  Panel,
  OnNodesChange,
  OnEdgesChange,
  ConnectionMode,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { useWorkflow } from '@/store/useWorkflow';
import TaskNode from './TaskNode';
import PersonaNode from './nodes/PersonaNode';
import ImageNode from './nodes/ImageNode';
import PromptNode from './nodes/PromptNode';
import VerticalWorkflowView from './VerticalWorkflowView';
import { Button } from './ui/button';
import { Play, Save, Square, LayoutGrid, List } from 'lucide-react';
import SaveWorkflowDialog from './SaveWorkflowDialog';
import { generateWorkflowFromConfig, executeWorkflowStep, WorkflowConfig } from '@/lib/workflowAutomation';

const nodeTypes = {
  task: TaskNode,
  persona: PersonaNode,
  image: ImageNode,
  prompt: PromptNode,
};

const defaultViewport = { x: 0, y: 0, zoom: 0.8 };

const connectionLineStyle = {
  strokeWidth: 2,
  stroke: '#b1b1b7',
  strokeDasharray: '5 5',
};

export default function Canvas() {
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [viewMode, setViewMode] = useState<'flow' | 'vertical'>('flow');
  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    executeWorkflow,
    currentExecutingNode,
    executionComplete,
    setNodes,
    setEdges,
    clearAll
  } = useWorkflow();

  console.log('Canvas: Current nodes:', nodes);
  console.log('Canvas: setNodes function:', typeof setNodes);

  // Component is now controlled entirely by the useWorkflow store
  // No need for event handling since PromptWorkspace calls store methods directly

  const handleExecuteWorkflow = async () => {
    setIsExecuting(true);
    await executeWorkflow();
    setIsExecuting(false);
  };

  return (
    <div className="w-full h-full relative">
      <VerticalWorkflowView />

      <SaveWorkflowDialog
        isOpen={isSaveDialogOpen}
        onClose={() => setIsSaveDialogOpen(false)}
      />
    </div>
  );
} 