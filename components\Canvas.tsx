import React, { useState, useEffect } from 'react';
import React<PERSON><PERSON>, {
  Background,
  Controls,
  MiniMap,
  Panel,
  OnNodesChange,
  OnEdgesChange,
  ConnectionMode,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { useWorkflow } from '@/store/useWorkflow';
import TaskNode from './TaskNode';
import PersonaNode from './nodes/PersonaNode';
import ImageNode from './nodes/ImageNode';
import PromptNode from './nodes/PromptNode';
import VerticalWorkflowView from './VerticalWorkflowView';
import { Button } from './ui/button';
import { Play, Save, Square, LayoutGrid, List } from 'lucide-react';
import SaveWorkflowDialog from './SaveWorkflowDialog';
import { generateWorkflowFromConfig, executeWorkflowStep, WorkflowConfig } from '@/lib/workflowAutomation';

const nodeTypes = {
  task: TaskNode,
  persona: PersonaNode,
  image: ImageNode,
  prompt: PromptNode,
};

const defaultViewport = { x: 0, y: 0, zoom: 0.8 };

const connectionLineStyle = {
  strokeWidth: 2,
  stroke: '#b1b1b7',
  strokeDasharray: '5 5',
};

export default function Canvas() {
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [viewMode, setViewMode] = useState<'flow' | 'vertical'>('flow');
  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    executeWorkflow,
    currentExecutingNode,
    executionComplete,
    setNodes,
    setEdges,
    clearAll
  } = useWorkflow();

  console.log('Canvas: Current nodes:', nodes);
  console.log('Canvas: setNodes function:', typeof setNodes);

  // Component is now controlled entirely by the useWorkflow store
  // No need for event handling since PromptWorkspace calls store methods directly

  const handleExecuteWorkflow = async () => {
    setIsExecuting(true);
    await executeWorkflow();
    setIsExecuting(false);
  };

  return (
    <div className="w-full h-full relative">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        defaultViewport={defaultViewport}
        fitView={false}
        snapToGrid={true}
        snapGrid={[15, 15]}
        className="absolute inset-0"
        connectionMode={ConnectionMode.Loose}
        connectionLineStyle={connectionLineStyle}
        connectionRadius={20}
      >
        <Background />
        <Controls />
        <MiniMap />

        {/* Top Panel with Controls */}
        <Panel position="top-right" className="flex gap-2">
          <Button
            onClick={handleExecuteWorkflow}
            disabled={nodes.length === 0 || isExecuting}
            className="bg-green-600 hover:bg-green-700 text-white"
            size="sm"
          >
            <Play className="w-4 h-4 mr-2" />
            {isExecuting ? 'Executing...' : 'Start Workflow'}
          </Button>

          <Button
            onClick={() => setIsSaveDialogOpen(true)}
            variant="outline"
            size="sm"
          >
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
        </Panel>

        {/* Execution Status */}
        {currentExecutingNode && (
          <Panel position="top-left" className="bg-blue-100 border border-blue-300 rounded-lg p-3">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-blue-800">
                Executing: {nodes.find(n => n.id === currentExecutingNode)?.data.title}
              </span>
            </div>
          </Panel>
        )}

        {/* Completion Status */}
        {executionComplete && (
          <Panel position="bottom-center" className="bg-green-100 border border-green-300 rounded-lg p-3">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium text-green-800">
                Workflow execution completed!
              </span>
            </div>
          </Panel>
        )}
      </ReactFlow>

      <SaveWorkflowDialog
        isOpen={isSaveDialogOpen}
        onClose={() => setIsSaveDialogOpen(false)}
      />
    </div>
  );
} 