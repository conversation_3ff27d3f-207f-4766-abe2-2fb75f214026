import React, { useState } from 'react';
import React<PERSON><PERSON>, {
  Background,
  Controls,
  MiniMap,
  Panel,
  OnNodesChange,
  OnEdgesChange,
  ConnectionMode,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { useWorkflow } from '@/store/useWorkflow';
import TaskNode from './TaskNode';
import { Button } from './ui/button';
import { Play, Save } from 'lucide-react';
import SaveWorkflowDialog from './SaveWorkflowDialog';

const nodeTypes = {
  task: TaskNode,
};

const defaultViewport = { x: 0, y: 0, zoom: 0.8 };

const connectionLineStyle = {
  strokeWidth: 2,
  stroke: '#b1b1b7',
  strokeDasharray: '5 5',
};

export default function Canvas() {
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    executeWorkflow,
    currentExecutingNode,
    executionComplete,
  } = useWorkflow();

  return (
    <div className="w-full h-full relative">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        defaultViewport={defaultViewport}
        fitView={false}
        snapToGrid={true}
        snapGrid={[15, 15]}
        className="absolute inset-0"
        connectionMode={ConnectionMode.Loose}
        connectionLineStyle={connectionLineStyle}
        connectionRadius={20}
      >
        <Background />
        <Controls />
        <MiniMap />
        <Panel position="top-right" className="flex gap-2">
          <Button
            onClick={executeWorkflow}
            disabled={currentExecutingNode !== null}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Play className="w-4 h-4 mr-2" />
            {currentExecutingNode ? 'Executing...' : 'Execute Workflow'}
          </Button>
          <Button
            onClick={() => setIsSaveDialogOpen(true)}
            className="bg-green-600 hover:bg-green-700"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Workflow
          </Button>
        </Panel>
      </ReactFlow>
      
      <SaveWorkflowDialog 
        isOpen={isSaveDialogOpen}
        onClose={() => setIsSaveDialogOpen(false)}
      />
    </div>
  );
} 