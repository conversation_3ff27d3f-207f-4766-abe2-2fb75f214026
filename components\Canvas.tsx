import React, { useState, useEffect } from 'react';
import React<PERSON><PERSON>, {
  Background,
  Controls,
  MiniMap,
  Panel,
  OnNodesChange,
  OnEdgesChange,
  ConnectionMode,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { useWorkflow } from '@/store/useWorkflow';
import TaskNode from './TaskNode';
import PersonaNode from './nodes/PersonaNode';
import ImageNode from './nodes/ImageNode';
import PromptNode from './nodes/PromptNode';
import { Button } from './ui/button';
import { Play, Save, Square } from 'lucide-react';
import SaveWorkflowDialog from './SaveWorkflowDialog';
import { generateWorkflowFromConfig, executeWorkflowStep, WorkflowConfig } from '@/lib/workflowAutomation';

const nodeTypes = {
  task: TaskNode,
  persona: PersonaNode,
  image: ImageNode,
  prompt: PromptNode,
};

const defaultViewport = { x: 0, y: 0, zoom: 0.8 };

const connectionLineStyle = {
  strokeWidth: 2,
  stroke: '#b1b1b7',
  strokeDasharray: '5 5',
};

export default function Canvas() {
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    executeWorkflow,
    currentExecutingNode,
    executionComplete,
    setNodes,
    setEdges,
    clearAll
  } = useWorkflow();

  // Listen for workflow creation events from AI Prompt Studio
  useEffect(() => {
    const handleCreateWorkflow = (event: CustomEvent<WorkflowConfig>) => {
      const config = event.detail;
      const { nodes: newNodes, edges: newEdges } = generateWorkflowFromConfig(config);

      // Clear existing workflow and set new one
      clearAll();
      setTimeout(() => {
        setNodes(newNodes);
        setEdges(newEdges);
      }, 100);
    };

    window.addEventListener('createWorkflow', handleCreateWorkflow as EventListener);

    return () => {
      window.removeEventListener('createWorkflow', handleCreateWorkflow as EventListener);
    };
  }, [setNodes, setEdges, clearAll]);

  const handleExecuteWorkflow = async () => {
    if (nodes.length === 0) return;

    setIsExecuting(true);

    // Find start node
    const startNode = nodes.find(node => node.data.type === 'start');
    if (!startNode) return;

    // Execute workflow step by step
    await executeWorkflowSequentially(startNode.id);

    setIsExecuting(false);
  };

  const executeWorkflowSequentially = async (currentNodeId: string) => {
    const currentNode = nodes.find(node => node.id === currentNodeId);
    if (!currentNode) return;

    // Update node status to executing
    setNodes(prevNodes =>
      prevNodes.map(node =>
        node.id === currentNodeId
          ? { ...node, data: { ...node.data, status: 'executing' } }
          : node
      )
    );

    // Execute the node if it's a prompt type
    if (currentNode.data.type !== 'start' && currentNode.data.type !== 'end') {
      try {
        const result = await executeWorkflowStep(currentNodeId, currentNode.data);

        // Update node with result
        setNodes(prevNodes =>
          prevNodes.map(node =>
            node.id === currentNodeId
              ? {
                  ...node,
                  data: {
                    ...node.data,
                    status: 'completed',
                    result: result.result,
                    timestamp: result.timestamp
                  }
                }
              : node
          )
        );
      } catch (error) {
        // Update node with error
        setNodes(prevNodes =>
          prevNodes.map(node =>
            node.id === currentNodeId
              ? {
                  ...node,
                  data: {
                    ...node.data,
                    status: 'error',
                    error: 'Failed to execute step'
                  }
                }
              : node
          )
        );
        return;
      }
    } else {
      // For start/end nodes, just mark as completed
      setNodes(prevNodes =>
        prevNodes.map(node =>
          node.id === currentNodeId
            ? { ...node, data: { ...node.data, status: 'completed' } }
            : node
        )
      );
    }

    // Find next nodes
    const nextEdges = edges.filter(edge => edge.source === currentNodeId);

    // Execute next nodes (parallel execution for multiple branches)
    if (nextEdges.length > 0) {
      await Promise.all(
        nextEdges.map(edge => executeWorkflowSequentially(edge.target))
      );
    }
  };

  return (
    <div className="w-full h-full relative">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        defaultViewport={defaultViewport}
        fitView={false}
        snapToGrid={true}
        snapGrid={[15, 15]}
        className="absolute inset-0"
        connectionMode={ConnectionMode.Loose}
        connectionLineStyle={connectionLineStyle}
        connectionRadius={20}
      >
        <Background />
        <Controls />
        <MiniMap />
        <Panel position="top-right" className="flex gap-2">
          <Button
            onClick={handleExecuteWorkflow}
            disabled={nodes.length === 0 || isExecuting}
            className="bg-green-600 hover:bg-green-700"
          >
            <Play className="w-4 h-4 mr-2" />
            {isExecuting ? 'Executing...' : 'Execute Workflow'}
          </Button>

          <Button
            onClick={() => setIsExecuting(false)}
            disabled={!isExecuting}
            variant="outline"
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            <Square className="w-4 h-4 mr-2" />
            Stop
          </Button>

          <Button
            onClick={() => setIsSaveDialogOpen(true)}
            disabled={nodes.length === 0}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Workflow
          </Button>
        </Panel>
      </ReactFlow>
      
      <SaveWorkflowDialog 
        isOpen={isSaveDialogOpen}
        onClose={() => setIsSaveDialogOpen(false)}
      />
    </div>
  );
} 