'use client'

import { useEffect } from 'react'
import { useProjectStore } from '@/lib/store/projects'
import { Button } from '@/components/ui/button'
import { Trash2, Edit3 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useUser } from '@clerk/nextjs'

export default function ProjectsPage() {
  const router = useRouter()
  const { user } = useUser()
  const { projects, loading, error, fetchProjects, deleteProject } = useProjectStore()

  useEffect(() => {
    if (user?.id) {
      fetchProjects(user.id)
    }
  }, [fetchProjects, user?.id])

  const handleDelete = async (projectId: string) => {
    if (!user?.id) return
    try {
      await deleteProject(user.id, projectId)
    } catch (error) {
      console.error('Failed to delete project:', error)
    }
  }

  if (!user) {
    return <div className="flex items-center justify-center min-h-screen">Please sign in to view projects</div>
  }

  if (loading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">My Projects</h1>
      </div>

      <div className="space-y-4">
        {projects.map((project) => (
          <div
            key={project.id}
            className="border rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex justify-between items-start mb-2">
              <h2 className="text-xl font-semibold">{project.name}</h2>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => router.push(`/projects/${project.id}`)}
                >
                  <Edit3 size={18} />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleDelete(project.id)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 size={18} />
                </Button>
              </div>
            </div>
            {project.description && (
              <p className="text-gray-600 text-sm mb-2">{project.description}</p>
            )}
            <div className="text-xs text-gray-500">
              Last updated: {new Date(project.updated_at).toLocaleDateString()}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
} 