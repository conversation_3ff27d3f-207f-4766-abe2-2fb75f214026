'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Sparkles,
  Workflow,
  ArrowLeft,
  Wand2,
  Image as ImageIcon
} from 'lucide-react';
import PromptWorkspace from '@/components/PromptWorkspace';
import Canvas from '@/components/Canvas';
import Sidebar from '@/components/Sidebar';
import { ReactFlowProvider } from 'reactflow';
import Header from '@/components/Header';

export default function CreatePage() {
  const [activeTab, setActiveTab] = useState('prompt-studio');

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="h-[calc(100vh-64px)]">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          {/* Tab Navigation */}
          <div className="border-b bg-white px-6 py-3">
            <div className="flex items-center justify-between">
              <TabsList className="grid w-auto grid-cols-2">
                <TabsTrigger value="prompt-studio" className="flex items-center gap-2">
                  <Wand2 className="w-4 h-4" />
                  AI Prompt Studio
                </TabsTrigger>
                <TabsTrigger value="workflow-builder" className="flex items-center gap-2">
                  <Workflow className="w-4 h-4" />
                  Workflow Builder
                </TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>AI Ready</span>
              </div>
            </div>
          </div>

          {/* Tab Content */}
          <TabsContent value="prompt-studio" className="h-[calc(100%-60px)] m-0">
            <PromptWorkspace />
          </TabsContent>

          <TabsContent value="workflow-builder" className="h-[calc(100%-60px)] m-0">
            <div className="flex h-full">
              <Sidebar />
              <div className="flex-1 relative">
                <ReactFlowProvider>
                  <Canvas />
                </ReactFlowProvider>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}