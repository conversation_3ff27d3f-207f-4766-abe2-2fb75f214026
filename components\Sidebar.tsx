'use client';

import React from "react";
import { Button } from "@/components/ui/button";
import { useWorkflow } from "@/store/useWorkflow";
import { Play, Square, CheckCircle, Trash2, FolderOpen } from "lucide-react";
import Link from "next/link";

export default function Sidebar() {
  const { addNode, clearAll } = useWorkflow();

  const addStartNode = () => {
    const newNode = {
      id: `start-${Date.now()}`,
      type: 'task',
      position: { x: 400, y: 100 },
      data: {
        title: 'Start',
        type: 'start',
      },
    };
    addNode(newNode);
  };

  const addTaskNode = () => {
    const newNode = {
      id: `task-${Date.now()}`,
      type: 'task',
      position: { x: 400, y: 200 },
      data: {
        title: 'New Task',
        description: 'Double-click to edit this task',
        minutes: 30,
        frequency: 'daily',
        difficulty: 3,
        type: 'task',
      },
    };
    addNode(newNode);
  };

  const addEndNode = () => {
    const newNode = {
      id: `end-${Date.now()}`,
      type: 'task',
      position: { x: 400, y: 300 },
      data: {
        title: 'End',
        type: 'end',
      },
    };
    addNode(newNode);
  };

  return (
    <div className="w-64 bg-white border-r border-gray-100 h-full overflow-y-auto shadow-sm">
      <div className="p-4 space-y-6">
        <div>
          <h2 className="text-sm font-medium text-gray-600 mb-3 px-1">Add Nodes</h2>
          <div className="space-y-2">
            <Button
              onClick={addStartNode}
              className="w-full justify-start bg-emerald-500 hover:bg-emerald-600 text-white"
              size="sm"
            >
              <Play className="w-4 h-4 mr-2" />
              Start
            </Button>
            
            <Button
              onClick={addTaskNode}
              className="w-full justify-start bg-blue-500 hover:bg-blue-600 text-white"
              size="sm"
            >
              <Square className="w-4 h-4 mr-2" />
              Task
            </Button>
            
            <Button
              onClick={addEndNode}
              className="w-full justify-start bg-red-500 hover:bg-red-600 text-white"
              size="sm"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              End
            </Button>
          </div>
        </div>

        <div>
          <h2 className="text-sm font-medium text-gray-600 mb-3 px-1">Workflow Actions</h2>
          <div className="space-y-2">
            <Button 
              variant="outline" 
              className="w-full justify-start border-gray-200 hover:bg-gray-50"
              size="sm"
              onClick={clearAll}
            >
              <Trash2 className="w-4 h-4 mr-2 text-gray-500" />
              Clear All
            </Button>
            
            <Link href="/projects" className="block">
              <Button 
                className="w-full justify-start bg-purple-500 hover:bg-purple-600 text-white"
                size="sm"
              >
                <FolderOpen className="w-4 h-4 mr-2" />
                My Projects
              </Button>
            </Link>
          </div>
        </div>

        <div>
          <h2 className="text-xs font-medium text-gray-600 mb-2">Instructions</h2>
          <ul className="text-xs text-gray-500 space-y-1">
            <li>• Click buttons above to add nodes</li>
            <li>• Drag nodes to reposition them</li>
            <li>• Connect nodes by dragging from handles</li>
            <li>• Double-click nodes to edit content</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
