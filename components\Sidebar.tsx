'use client';

import React from "react";
import { Button } from "@/components/ui/button";
import { useWorkflow } from "@/store/useWorkflow";
import {
  Play,
  Square,
  CheckCircle,
  Trash2,
  FolderOpen,
  User,
  Image as ImageIcon,
  Wand2,
  Video,
  Type,
  Target,
  Sparkles
} from "lucide-react";
import Link from "next/link";

export default function Sidebar() {
  const { addNode, clearAll, executeWorkflow, nodes } = useWorkflow();

  const addStartNode = () => {
    const newNode = {
      id: `start-${Date.now()}`,
      type: 'task',
      position: { x: 400, y: 100 },
      data: {
        title: 'Start',
        type: 'start',
      },
    };
    addNode(newNode);
  };

  const addTaskNode = () => {
    const newNode = {
      id: `task-${Date.now()}`,
      type: 'task',
      position: { x: 400, y: 200 },
      data: {
        title: 'New Task',
        description: 'Double-click to edit this task',
        minutes: 30,
        frequency: 'daily',
        difficulty: 3,
        type: 'task',
      },
    };
    addNode(newNode);
  };

  const addEndNode = () => {
    const newNode = {
      id: `end-${Date.now()}`,
      type: 'task',
      position: { x: 400, y: 300 },
      data: {
        title: 'End',
        type: 'end',
      },
    };
    addNode(newNode);
  };

  const addPersonaNode = () => {
    const newNode = {
      id: `persona-${Date.now()}`,
      type: 'persona',
      position: { x: 100, y: 100 },
      data: {
        title: 'Brand Persona',
        description: 'Define your brand identity',
        type: 'persona',
        config: {
          industry: 'general',
          defaultTone: 'professional',
          suggestedKeywords: ['modern', 'clean', 'professional']
        }
      },
    };
    addNode(newNode);
  };

  const addImageNode = () => {
    const newNode = {
      id: `image-${Date.now()}`,
      type: 'image',
      position: { x: 400, y: 100 },
      data: {
        title: 'Upload Image',
        description: 'Add reference image for analysis',
        type: 'image',
        config: {
          acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
          maxSize: 5
        }
      },
    };
    addNode(newNode);
  };

  const addVisualPromptNode = () => {
    const newNode = {
      id: `visual-${Date.now()}`,
      type: 'prompt',
      position: { x: 700, y: 100 },
      data: {
        title: 'Visual Prompt',
        description: 'Generate AI image prompts',
        type: 'visual',
        config: {
          platforms: ['instagram', 'pinterest', 'tiktok'],
          aspectRatios: ['1:1', '4:5', '16:9', '9:16'],
          cameraStyles: ['professional', 'lifestyle', 'product']
        }
      },
    };
    addNode(newNode);
  };

  const addReelsNode = () => {
    const newNode = {
      id: `reels-${Date.now()}`,
      type: 'prompt',
      position: { x: 400, y: 300 },
      data: {
        title: 'Reels Script',
        description: 'Create engaging video scripts',
        type: 'reels',
        config: {
          platforms: ['instagram', 'tiktok', 'youtube'],
          aspectRatios: ['9:16']
        }
      },
    };
    addNode(newNode);
  };

  const addCaptionNode = () => {
    const newNode = {
      id: `caption-${Date.now()}`,
      type: 'prompt',
      position: { x: 700, y: 300 },
      data: {
        title: 'Caption Generator',
        description: 'Write compelling captions',
        type: 'caption',
        config: {
          platforms: ['instagram', 'facebook', 'linkedin']
        }
      },
    };
    addNode(newNode);
  };

  const addAdCopyNode = () => {
    const newNode = {
      id: `ad-${Date.now()}`,
      type: 'prompt',
      position: { x: 1000, y: 200 },
      data: {
        title: 'Ad Copy',
        description: 'Generate advertising copy',
        type: 'ad',
        config: {
          platforms: ['facebook', 'google', 'instagram']
        }
      },
    };
    addNode(newNode);
  };

  return (
    <div className="w-80 bg-white border-r border-gray-100 h-full overflow-y-auto shadow-sm">
      <div className="p-4 space-y-6">
        {/* Start Workflow Button */}
        <div>
          <Button
            onClick={executeWorkflow}
            disabled={nodes.length === 0}
            className="w-full bg-green-600 hover:bg-green-700 text-white py-3 text-lg font-semibold"
            size="lg"
          >
            <Play className="w-5 h-5 mr-2" />
            Start Workflow
          </Button>
        </div>
        {/* AI Prompt Nodes */}
        <div>
          <h2 className="text-sm font-medium text-gray-600 mb-3 px-1 flex items-center gap-2">
            <Sparkles className="w-4 h-4" />
            AI Prompt Nodes
          </h2>
          <div className="space-y-2">
            <Button
              onClick={addPersonaNode}
              className="w-full justify-start bg-purple-500 hover:bg-purple-600 text-white"
              size="sm"
            >
              <User className="w-4 h-4 mr-2" />
              Brand Persona
            </Button>

            <Button
              onClick={addImageNode}
              className="w-full justify-start bg-blue-500 hover:bg-blue-600 text-white"
              size="sm"
            >
              <ImageIcon className="w-4 h-4 mr-2" />
              Image Upload
            </Button>

            <Button
              onClick={addVisualPromptNode}
              className="w-full justify-start bg-purple-600 hover:bg-purple-700 text-white"
              size="sm"
            >
              <Wand2 className="w-4 h-4 mr-2" />
              Visual Prompt
            </Button>

            <Button
              onClick={addReelsNode}
              className="w-full justify-start bg-green-500 hover:bg-green-600 text-white"
              size="sm"
            >
              <Video className="w-4 h-4 mr-2" />
              Reels Script
            </Button>

            <Button
              onClick={addCaptionNode}
              className="w-full justify-start bg-blue-600 hover:bg-blue-700 text-white"
              size="sm"
            >
              <Type className="w-4 h-4 mr-2" />
              Caption
            </Button>

            <Button
              onClick={addAdCopyNode}
              className="w-full justify-start bg-orange-500 hover:bg-orange-600 text-white"
              size="sm"
            >
              <Target className="w-4 h-4 mr-2" />
              Ad Copy
            </Button>
          </div>
        </div>

        {/* Basic Workflow Nodes */}
        <div>
          <h2 className="text-sm font-medium text-gray-600 mb-3 px-1">Basic Nodes</h2>
          <div className="space-y-2">
            <Button
              onClick={addStartNode}
              className="w-full justify-start bg-emerald-500 hover:bg-emerald-600 text-white"
              size="sm"
            >
              <Play className="w-4 h-4 mr-2" />
              Start
            </Button>

            <Button
              onClick={addTaskNode}
              className="w-full justify-start bg-gray-500 hover:bg-gray-600 text-white"
              size="sm"
            >
              <Square className="w-4 h-4 mr-2" />
              Task
            </Button>

            <Button
              onClick={addEndNode}
              className="w-full justify-start bg-red-500 hover:bg-red-600 text-white"
              size="sm"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              End
            </Button>
          </div>
        </div>

        <div>
          <h2 className="text-sm font-medium text-gray-600 mb-3 px-1">Workflow Actions</h2>
          <div className="space-y-2">
            <Button 
              variant="outline" 
              className="w-full justify-start border-gray-200 hover:bg-gray-50"
              size="sm"
              onClick={clearAll}
            >
              <Trash2 className="w-4 h-4 mr-2 text-gray-500" />
              Clear All
            </Button>
            
            <Link href="/projects" className="block">
              <Button 
                className="w-full justify-start bg-purple-500 hover:bg-purple-600 text-white"
                size="sm"
              >
                <FolderOpen className="w-4 h-4 mr-2" />
                My Projects
              </Button>
            </Link>
          </div>
        </div>

        <div>
          <h2 className="text-xs font-medium text-gray-600 mb-2">Instructions</h2>
          <ul className="text-xs text-gray-500 space-y-1">
            <li>• Click buttons above to add nodes</li>
            <li>• Drag nodes to reposition them</li>
            <li>• Connect nodes by dragging from handles</li>
            <li>• Double-click nodes to configure them</li>
            <li>• Start with Brand Persona → Image → Visual Prompt</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
