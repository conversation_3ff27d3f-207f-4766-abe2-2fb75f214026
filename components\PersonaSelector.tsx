'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  User, 
  Plus, 
  Palette, 
  Target, 
  Sparkles,
  Check,
  X
} from 'lucide-react';
import { usePromptStore, BrandPersona } from '@/store/usePromptStore';
import { useUser } from '@clerk/nextjs';

const INDUSTRIES = [
  'skincare', 'fashion', 'food', 'technology', 'wellness', 'creative', 'beauty', 'lifestyle', 'business', 'education'
];

const TONE_OPTIONS = [
  'professional', 'friendly', 'luxury', 'playful', 'minimalist', 'bold', 'warm', 'sophisticated', 'energetic', 'calm'
];

const PLATFORMS = [
  'instagram', 'tiktok', 'pinterest', 'facebook', 'linkedin', 'youtube', 'twitter', 'snapchat'
];

export default function PersonaSelector() {
  const { user } = useUser();
  const {
    personas,
    selectedPersona,
    isLoading,
    selectPersona,
    createPersona
  } = usePromptStore();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newPersona, setNewPersona] = useState({
    name: '',
    description: '',
    industry: '',
    tone_of_voice: '',
    style_keywords: [] as string[],
    target_platforms: [] as string[],
    color_palette: { primary: '#6366f1', secondary: '#f8fafc', accent: '#8b5cf6' }
  });

  const handleCreatePersona = async () => {
    if (!user?.id || !newPersona.name.trim()) return;

    try {
      await createPersona(user.id, {
        ...newPersona,
        style_keywords: newPersona.style_keywords.filter(k => k.trim()),
        target_platforms: newPersona.target_platforms
      });
      
      setShowCreateForm(false);
      setNewPersona({
        name: '',
        description: '',
        industry: '',
        tone_of_voice: '',
        style_keywords: [],
        target_platforms: [],
        color_palette: { primary: '#6366f1', secondary: '#f8fafc', accent: '#8b5cf6' }
      });
    } catch (error) {
      console.error('Error creating persona:', error);
    }
  };

  const addStyleKeyword = (keyword: string) => {
    if (keyword.trim() && !newPersona.style_keywords.includes(keyword.trim())) {
      setNewPersona(prev => ({
        ...prev,
        style_keywords: [...prev.style_keywords, keyword.trim()]
      }));
    }
  };

  const removeStyleKeyword = (keyword: string) => {
    setNewPersona(prev => ({
      ...prev,
      style_keywords: prev.style_keywords.filter(k => k !== keyword)
    }));
  };

  const togglePlatform = (platform: string) => {
    setNewPersona(prev => ({
      ...prev,
      target_platforms: prev.target_platforms.includes(platform)
        ? prev.target_platforms.filter(p => p !== platform)
        : [...prev.target_platforms, platform]
    }));
  };

  if (showCreateForm) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Plus className="w-5 h-5" />
              Create Brand Persona
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowCreateForm(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Persona Name</label>
              <Input
                placeholder="e.g., Luxury Skincare Brand"
                value={newPersona.name}
                onChange={(e) => setNewPersona(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Industry</label>
              <Select
                value={newPersona.industry}
                onValueChange={(value) => setNewPersona(prev => ({ ...prev, industry: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select industry" />
                </SelectTrigger>
                <SelectContent>
                  {INDUSTRIES.map((industry) => (
                    <SelectItem key={industry} value={industry}>
                      {industry.charAt(0).toUpperCase() + industry.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Description</label>
            <Textarea
              placeholder="Describe your brand personality and values..."
              value={newPersona.description}
              onChange={(e) => setNewPersona(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Tone of Voice</label>
            <Select
              value={newPersona.tone_of_voice}
              onValueChange={(value) => setNewPersona(prev => ({ ...prev, tone_of_voice: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select tone" />
              </SelectTrigger>
              <SelectContent>
                {TONE_OPTIONS.map((tone) => (
                  <SelectItem key={tone} value={tone}>
                    {tone.charAt(0).toUpperCase() + tone.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Style Keywords</label>
            <div className="flex flex-wrap gap-2 mb-2">
              {newPersona.style_keywords.map((keyword) => (
                <Badge key={keyword} variant="secondary" className="flex items-center gap-1">
                  {keyword}
                  <button
                    onClick={() => removeStyleKeyword(keyword)}
                    className="ml-1 hover:text-red-500"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <Input
              placeholder="Add style keywords (press Enter)"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  addStyleKeyword(e.currentTarget.value);
                  e.currentTarget.value = '';
                }
              }}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Target Platforms</label>
            <div className="flex flex-wrap gap-2">
              {PLATFORMS.map((platform) => (
                <Badge
                  key={platform}
                  variant={newPersona.target_platforms.includes(platform) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => togglePlatform(platform)}
                >
                  {platform.charAt(0).toUpperCase() + platform.slice(1)}
                  {newPersona.target_platforms.includes(platform) && (
                    <Check className="w-3 h-3 ml-1" />
                  )}
                </Badge>
              ))}
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              onClick={handleCreatePersona}
              disabled={!newPersona.name.trim() || isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Persona
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowCreateForm(false)}
            >
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium">Brand Persona</label>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowCreateForm(true)}
        >
          <Plus className="w-4 h-4 mr-1" />
          Create New
        </Button>
      </div>

      {personas.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="p-6 text-center">
            <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="font-medium text-gray-900 mb-2">No personas yet</h3>
            <p className="text-sm text-gray-600 mb-4">
              Create your first brand persona to get started with AI prompt generation
            </p>
            <Button onClick={() => setShowCreateForm(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Persona
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Select
          value={selectedPersona?.id || ''}
          onValueChange={(value) => {
            const persona = personas.find(p => p.id === value);
            if (persona) selectPersona(persona);
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a brand persona" />
          </SelectTrigger>
          <SelectContent>
            {personas.map((persona) => (
              <SelectItem key={persona.id} value={persona.id}>
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: persona.color_palette?.primary || '#6366f1' }}
                  />
                  <span>{persona.name}</span>
                  {persona.is_template && (
                    <Badge variant="secondary" className="text-xs">Template</Badge>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      {selectedPersona && (
        <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div
                className="w-10 h-10 rounded-xl flex items-center justify-center"
                style={{ backgroundColor: selectedPersona.color_palette?.primary || '#6366f1' }}
              >
                <User className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{selectedPersona.name}</h3>
                <p className="text-sm text-gray-600 mb-2">{selectedPersona.description}</p>
                <div className="flex flex-wrap gap-1">
                  <Badge variant="outline" className="text-xs">
                    {selectedPersona.tone_of_voice}
                  </Badge>
                  {selectedPersona.style_keywords?.slice(0, 3).map((keyword) => (
                    <Badge key={keyword} variant="secondary" className="text-xs">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
