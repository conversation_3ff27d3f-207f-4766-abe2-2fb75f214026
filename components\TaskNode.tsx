'use client';

import React, { useState } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "reactflow";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Clock, 
  Zap, 
  AlertTriangle, 
  ExternalLink, 
  Sparkles, 
  X, 
  ChevronDown, 
  ChevronUp, 
  MoreHorizontal,
  Loader2
} from "lucide-react";
import { useWorkflow } from "@/store/useWorkflow";
import { motion } from "framer-motion";

interface TaskNodeData {
  title: string;
  description?: string;
  minutes?: number;
  frequency?: string;
  difficulty?: number;
  painPoint?: boolean;
  type?: 'start' | 'task' | 'end';
}

export default function TaskNode({ data, selected, id }: NodeProps<TaskNodeData>) {
  const { 
    currentExecutingNode, 
    executionComplete, 
    suggestions, 
    deleteNode,
    collapsedSuggestions,
    toggleSuggestions,
    executeWorkflow
  } = useWorkflow();
  
  const [expandedCards, setExpandedCards] = useState<Record<number, boolean>>({});
  
  const isCurrentlyExecuting = currentExecutingNode === id;
  const nodeSuggestions = suggestions[id || ''] || [];
  const showSuggestions = executionComplete && data.type === 'task' && nodeSuggestions.length > 0;
  const isCollapsed = collapsedSuggestions[id || ''] || false;

  const toggleCardExpansion = (cardIndex: number, e: React.MouseEvent) => {
    e.stopPropagation();
    setExpandedCards(prev => ({
      ...prev,
      [cardIndex]: !prev[cardIndex]
    }));
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (id) {
      deleteNode(id);
    }
  };

  const openTool = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  // For Start nodes, render just a simple green button
  if (data.type === 'start') {
    return (
      <div className="relative workflow-node group">
        <Handle
          type="source"
          position={Position.Right}
          id="start-output"
          className="w-3 h-3 !bg-gray-400 border-2 border-white"
        />
        <Button
          onClick={handleDelete}
          className="absolute -top-2 -right-2 w-6 h-6 p-0 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10"
          size="sm"
        >
          <X className="w-3 h-3" />
        </Button>
        <motion.div 
          onClick={executeWorkflow}
          className={`px-6 py-3 rounded-full font-semibold text-sm shadow-lg ${
            isCurrentlyExecuting 
              ? 'bg-green-400 text-white ring-4 ring-green-300' 
              : 'bg-green-500 text-white'
          }`}
          animate={isCurrentlyExecuting ? { scale: [1, 1.1, 1] } : {}}
          transition={{ duration: 1, repeat: isCurrentlyExecuting ? Infinity : 0 }}
        >
          {isCurrentlyExecuting ? (
            <div className="flex items-center gap-2">
              <Loader2 className="w-4 h-4 animate-spin" />
              Running...
            </div>
          ) : (
            'Start'
          )}
        </motion.div>
      </div>
    );
  }

  // For End nodes, render just a simple red button
  if (data.type === 'end') {
    return (
      <div className="relative workflow-node group">
        <Handle
          type="target"
          position={Position.Left}
          id="end-input"
          className="w-3 h-3 !bg-gray-400 border-2 border-white"
        />
        <Button
          onClick={handleDelete}
          className="absolute -top-2 -right-2 w-6 h-6 p-0 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10"
          size="sm"
        >
          <X className="w-3 h-3" />
        </Button>
        <motion.div 
          className={`px-6 py-3 rounded-full font-semibold text-sm shadow-lg ${
            isCurrentlyExecuting 
              ? 'bg-red-400 text-white ring-4 ring-red-300' 
              : 'bg-red-500 text-white'
          }`}
          animate={isCurrentlyExecuting ? { scale: [1, 1.1, 1] } : {}}
          transition={{ duration: 1, repeat: isCurrentlyExecuting ? Infinity : 0 }}
        >
          {isCurrentlyExecuting ? (
            <div className="flex items-center gap-2">
              <Loader2 className="w-4 h-4 animate-spin" />
              Completing...
            </div>
          ) : (
            'End'
          )}
        </motion.div>
      </div>
    );
  }

  // For task nodes, render the full card
  return (
    <div className="relative workflow-node space-y-3 group">
      <Handle
        type="target"
        position={Position.Left}
        id="task-input"
        className="w-3 h-3 !bg-gray-400 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="task-output"
        className="w-3 h-3 !bg-gray-400 border-2 border-white"
      />
      
      <Button
        onClick={handleDelete}
        className="absolute -top-2 -right-2 w-6 h-6 p-0 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10"
        size="sm"
      >
        <X className="w-3 h-3" />
      </Button>
      
      <Card className={`w-[280px] shadow-lg border-2 bg-white dark:bg-gray-800 ${
        isCurrentlyExecuting 
          ? 'border-yellow-400 shadow-xl ring-4 ring-yellow-200' 
          : selected 
            ? 'border-blue-500 shadow-xl' 
            : 'border-gray-200 dark:border-gray-600'
      }`}>
        <CardContent className="p-4 space-y-3">
          <div className="space-y-2">
            <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
              {data.title}
            </h3>
            
            {data.description && (
              <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-300">
                {data.description}
              </p>
            )}
          </div>

          {(data.minutes || data.frequency || data.difficulty) && (
            <div className="flex flex-wrap gap-2 pt-2">
              {data.minutes && (
                <Badge variant="outline" className="flex items-center gap-1 text-xs">
                  <Clock className="w-3 h-3" />
                  {data.minutes}min
                </Badge>
              )}
              
              {data.frequency && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs">
                  <Zap className="w-3 h-3" />
                  {data.frequency}
                </Badge>
              )}
              
              {data.difficulty && (
                <Badge 
                  variant="destructive" 
                  className="flex items-center gap-1 text-xs"
                >
                  Difficulty {data.difficulty}/5
                </Badge>
              )}
            </div>
          )}

          {data.painPoint && (
            <div className="flex items-center gap-2 pt-2">
              <AlertTriangle className="w-4 h-4 text-amber-500" />
              <span className="text-xs text-amber-600 font-medium">
                Pain Point
              </span>
            </div>
          )}

          {isCurrentlyExecuting && (
            <div className="pt-2">
              <div className="flex items-center gap-2 text-yellow-600 bg-yellow-50 p-2 rounded-md">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-xs font-medium">Processing...</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* AI Suggestions - Show below card after execution completes */}
      {showSuggestions && (
        <div className="w-[200px] mt-2 space-y-2">
          <div 
            className="flex items-center justify-between text-violet-600 dark:text-violet-400 text-xs font-medium cursor-pointer hover:text-violet-700 dark:hover:text-violet-300 transition-colors"
            onClick={() => id && toggleSuggestions(id)}
          >
            <div className="flex items-center gap-1">
              <Sparkles className="w-3 h-3" />
              AI Suggestions ({nodeSuggestions.length})
            </div>
            {isCollapsed ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronUp className="w-3 h-3" />
            )}
          </div>
          
          {!isCollapsed && (
            <div className="space-y-2 animate-in slide-in-from-top-2 duration-200">
              {nodeSuggestions.map((suggestion, index) => {
                const isExpanded = expandedCards[index];
                return (
                  <div
                    key={index}
                    className="p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors shadow-sm"
                  >
                    <div className="space-y-2">
                      {/* Tool name and actions */}
                      <div className="flex items-start justify-between gap-2">
                        <h4 
                          className="text-sm font-medium text-gray-900 dark:text-white leading-tight cursor-pointer flex-1 min-w-0"
                          onClick={() => openTool(suggestion.url)}
                        >
                          {suggestion.name}
                        </h4>
                        <div className="flex items-center gap-1 flex-shrink-0">
                          <Button
                            onClick={(e) => toggleCardExpansion(index, e)}
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-gray-200 dark:hover:bg-gray-600"
                          >
                            <MoreHorizontal className="w-3 h-3" />
                          </Button>
                          <ExternalLink 
                            className="w-4 h-4 text-gray-400 dark:text-gray-500 cursor-pointer" 
                            onClick={() => openTool(suggestion.url)}
                          />
                        </div>
                      </div>
                      
                      {/* Category badge - ensure it doesn't overflow */}
                      <div className="flex items-center">
                        <Badge 
                          variant="outline" 
                          className="text-xs px-2 py-0.5 text-blue-700 dark:text-blue-300 border-blue-300 dark:border-blue-500 bg-blue-50 dark:bg-blue-900/30 max-w-full truncate"
                        >
                          {suggestion.category}
                        </Badge>
                      </div>
                      
                      {/* Description - expandable */}
                      <p 
                        className={`text-xs text-gray-600 dark:text-gray-300 leading-relaxed cursor-pointer ${
                          isExpanded ? '' : 'line-clamp-2'
                        }`}
                        onClick={() => openTool(suggestion.url)}
                      >
                        {suggestion.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
