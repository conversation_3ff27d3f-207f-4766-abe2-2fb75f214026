-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 1. Users can read their own data
CREATE POLICY "Users can read own data"
ON users FOR SELECT
USING (clerk_id = auth.uid()::text);

-- 2. Users can update their own non-sensitive data
CREATE POLICY "Users can update own profile"
ON users FOR UPDATE
USING (clerk_id = auth.uid()::text)
WITH CHECK (
  clerk_id = auth.uid()::text
  AND (plan_type IS NULL OR plan_type = 'free')
  AND (credits_remaining IS NULL OR credits_remaining = 10)
);

-- 3. Service role can manage all user data
CREATE POLICY "Service role full access"
ON users
FOR ALL
USING (auth.jwt()->>'role' = 'service_role')
WITH CHECK (auth.jwt()->>'role' = 'service_role');

-- 4. Prevent manual deletion
CREATE POLICY "No manual deletion"
ON users FOR DELETE
USING (false);

-- Grant access to authenticated users
GRANT SELECT, UPDATE (full_name, avatar_url) ON users TO authenticated; 