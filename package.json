{"name": "ai-flowmap", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/clerk-sdk-node": "^4.13.23", "@clerk/nextjs": "^6.24.0", "@google/genai": "^1.8.0", "@hookform/resolvers": "^5.1.1", "@next/font": "^14.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@reactflow/background": "^11.3.14", "@reactflow/controls": "^11.2.14", "@reactflow/core": "^11.11.4", "@reactflow/minimap": "^11.7.14", "@supabase/supabase-js": "^2.50.5", "@types/html2canvas": "^0.5.35", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.3", "html2canvas": "^1.4.1", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "reactflow": "^11.11.4", "sonner": "^2.0.6", "svix": "^1.69.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}