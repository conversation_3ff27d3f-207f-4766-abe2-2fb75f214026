import { createClient, SupabaseClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

interface CachedClient {
  client: SupabaseClient;
  userId: string;
}

let cachedClient: CachedClient | null = null;

// Client for authenticated users (client-side only)
export const createSupabaseClient = (clerkUserId?: string) => {
  // If no user ID is provided, throw an error
  if (!clerkUserId) {
    throw new Error('User ID is required to create a Supabase client');
  }

  // If we have a cached client with the same user ID, return it
  if (cachedClient && cachedClient.userId === clerkUserId) {
    return cachedClient.client;
  }

  // Create a new Supabase client
  const client = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
    global: {
      headers: {
        'x-clerk-user-id': clerkUserId,
      },
    },
  });

  // Cache the new client with the user ID
  cachedClient = {
    client,
    userId: clerkUserId,
  };

  return client;
}; 