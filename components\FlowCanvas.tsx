'use client';

import React, { use<PERSON><PERSON>back, useEffect } from "react";
import <PERSON>act<PERSON><PERSON>, {
  Background,
  Controls,
  Connection,
  useNodesState,
  useEdgesState,
  Node,
  OnSelectionChangeParams,
  Panel,
} from "reactflow";
import "reactflow/dist/style.css";
import { useWorkflow } from "@/store/useWorkflow";
import { Button } from "@/components/ui/button";
import { Play, Save } from "lucide-react";

import TaskNode from "./TaskNode";

const nodeTypes = {
  task: TaskNode,
};

interface FlowCanvasProps {
  onNodeSelect?: (node: Node | null) => void;
}

export default function FlowCanvas({ onNodeSelect }: FlowCanvasProps) {
  const { nodes, edges, onConnect, executeWorkflow } = useWorkflow();

  const [localNodes, setLocalNodes, onNodesChange] = useNodesState([]);
  const [localEdges, setLocalEdges, onEdgesChange] = useEdgesState([]);

  // Sync global state to local state - preserve existing positions
  useEffect(() => {
    setLocalNodes(prevNodes => {
      // If we're adding new nodes, preserve existing node positions
      if (nodes.length > prevNodes.length) {
        const newNodes = nodes.filter(node => !prevNodes.find(prev => prev.id === node.id));
        return [...prevNodes, ...newNodes];
      }
      // For other changes, use the new nodes but preserve positions where possible
      return nodes.map(node => {
        const existingNode = prevNodes.find(prev => prev.id === node.id);
        if (existingNode) {
          // Preserve the position of existing nodes
          return { ...node, position: existingNode.position };
        }
        return node;
      });
    });
  }, [nodes, setLocalNodes]);

  useEffect(() => {
    setLocalEdges(edges);
  }, [edges, setLocalEdges]);

  const handleConnect = useCallback(
    (connection: Connection) => {
      onConnect(connection);
    },
    [onConnect]
  );

  const handleSelectionChange = useCallback(
    (params: OnSelectionChangeParams) => {
      if (onNodeSelect) {
        const selectedNode = params.nodes.length > 0 ? params.nodes[0] : null;
        onNodeSelect(selectedNode);
      }
    },
    [onNodeSelect]
  );

  return (
    <div className="w-full h-full rounded-md border border-gray-200 bg-white shadow-md relative">
      <ReactFlow
        nodes={localNodes}
        edges={localEdges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={handleConnect}
        onSelectionChange={handleSelectionChange}
        nodeTypes={nodeTypes}
        fitView={false}
        fitViewOptions={{ padding: 0.2 }}
        preventScrolling={false}
        zoomOnScroll={true}
        zoomOnPinch={true}
        panOnScroll={true}
        panOnScrollSpeed={0.5}
        zoomOnDoubleClick={false}
        attributionPosition="bottom-left"
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        minZoom={0.1}
        maxZoom={4}
        className="bg-slate-50"
      >
        <Background 
          gap={16} 
          size={1} 
          color="#e2e8f0" 
          style={{ backgroundColor: '#f8fafc' }}
        />
        <Controls className="bg-white border border-gray-200 shadow-md rounded-lg p-1" />
        
        <Panel position="top-right" className="space-x-2">
          <Button
            size="sm"
            onClick={executeWorkflow}
            className="bg-green-500 hover:bg-green-600 text-white shadow-md"
          >
            <Play className="w-4 h-4 mr-2" />
            Run Workflow
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="bg-white shadow-md"
          >
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
        </Panel>
      </ReactFlow>
    </div>
  );
}
