-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies
-- 1. Allow users to read their own data
CREATE POLICY "Users can view own data"
ON users
FOR SELECT
USING (auth.uid()::text = id);

-- 2. Allow users to update their own data
CREATE POLICY "Users can update own data"
ON users
FOR UPDATE
USING (auth.uid()::text = id)
WITH CHECK (auth.uid()::text = id);

-- 3. Allow insert through the service role only
CREATE POLICY "Service role can insert"
ON users
FOR INSERT
WITH CHECK (true);

-- 4. Prevent manual deletion
CREATE POLICY "Prevent manual deletion"
ON users
FOR DELETE
USING (false);

-- Grant necessary permissions to authenticated users
GRANT SELECT, UPDATE ON users TO authenticated; 