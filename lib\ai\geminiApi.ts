import { generateVisualPrompt } from './visualPrompt';
import { generateReelsPrompt } from './reelsPrompt';
import { generateCaptionPrompt } from './captionPrompt';
import { generateAdCopyPrompt } from './adCopyPrompt';

export interface GeminiConfig {
  apiKey?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface PromptRequest {
  type: 'visual' | 'reels' | 'caption' | 'ad';
  config: any;
  customPrompt?: string;
}

class GeminiAPI {
  private apiKey: string;
  private model: string;
  private baseUrl: string;

  constructor(config: GeminiConfig = {}) {
    this.apiKey = config.apiKey || process.env.NEXT_PUBLIC_GEMINI_API_KEY || '';
    this.model = config.model || 'gemini-1.5-flash';
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
  }

  async generateContent(prompt: string, options: { temperature?: number; maxTokens?: number } = {}): Promise<string> {
    if (!this.apiKey) {
      console.warn('Gemini API key not found, using mock response');
      return this.getMockResponse(prompt);
    }

    try {
      const response = await fetch(`${this.baseUrl}/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: options.temperature || 0.7,
            maxOutputTokens: options.maxTokens || 2048,
            topP: 0.8,
            topK: 40
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        return data.candidates[0].content.parts[0].text;
      } else {
        throw new Error('Invalid response format from Gemini API');
      }
    } catch (error) {
      console.error('Gemini API error:', error);
      return this.getMockResponse(prompt);
    }
  }

  private getMockResponse(prompt: string): string {
    // Determine response type based on prompt content
    if (prompt.includes('visual') || prompt.includes('photography')) {
      return `📸 PROFESSIONAL VISUAL PROMPT

**SCENE DESCRIPTION:**
Professional lifestyle photography featuring modern, clean aesthetic with natural lighting. Composition follows rule of thirds with engaging subject placement.

**TECHNICAL DETAILS:**
- Camera: Professional DSLR with 85mm lens
- Aperture: f/2.8 for optimal depth of field  
- Lighting: Natural window light with soft shadows
- Color grading: Warm, inviting tones

**STYLING NOTES:**
- Props: Minimal, purposeful elements
- Colors: Brand-consistent palette
- Mood: Professional yet approachable
- Platform: Optimized for social media engagement

This visual will perfectly represent your brand while driving maximum engagement.`;
    }

    if (prompt.includes('reels') || prompt.includes('video')) {
      return `🎬 ENGAGING REELS SCRIPT

**HOOK (0-3s):**
"Wait... did you know this secret?"
[Visual: Close-up shot with dramatic lighting]

**MAIN CONTENT (3-25s):**
[Scene 1] Quick transition showing the problem
[Scene 2] Reveal the solution with energetic pacing
[Scene 3] Show the transformation/result

**CTA (25-30s):**
"Follow for more tips like this! 👆"
[Visual: Clear brand logo/handle display]

**Production Notes:**
- Use trending audio
- Add text overlays for key points
- Include brand color scheme
- Film in good natural lighting`;
    }

    if (prompt.includes('caption')) {
      return `✨ Ready to transform your social media game? 

Here's what we've learned about creating engaging content...

Our research shows that the key to success lies in understanding your audience and delivering value consistently.

💭 What's your biggest challenge with social media content? Drop a comment below - we read every single one!

👆 Follow @yourbrand for more insights like this.

#socialmedia #contentcreator #brandstory #engagement #marketing #trending #community #growth`;
    }

    if (prompt.includes('ad') || prompt.includes('advertising')) {
      return `🎯 HIGH-CONVERTING AD COPY VARIATIONS

**VARIATION A - Benefit-Focused:**
Headline: "Transform Your Business in 30 Days"
Primary Text: Stop struggling with ineffective marketing. Our proven system has helped 10,000+ businesses achieve results. Limited time offer - 50% off!
CTA: "Get Started Now"

**VARIATION B - Problem-Solution:**
Headline: "Finally, A Solution That Works"
Primary Text: Tired of marketing that doesn't deliver? Join thousands who've discovered the easier way. See results in just 7 days or money back.
CTA: "Try Risk-Free"

**VARIATION C - Social Proof:**
Headline: "Join 50,000+ Happy Customers"
Primary Text: "Best investment I've made!" - Sarah K. ⭐⭐⭐⭐⭐ Don't miss out on what everyone's talking about.
CTA: "Join Now"`;
    }

    return `🚀 PROFESSIONAL CONTENT GENERATED

Thank you for using our AI-powered content generation system. Your request has been processed and optimized for maximum engagement.

**Key Features:**
- Brand-aligned messaging
- Platform-optimized format
- Engagement-driving elements
- Professional quality output

This content is ready to use and will help you achieve your marketing goals effectively.`;
  }

  async generatePromptContent(request: PromptRequest): Promise<string> {
    let prompt: string;

    // Use custom prompt if provided, otherwise generate based on type
    if (request.customPrompt) {
      prompt = request.customPrompt;
    } else {
      switch (request.type) {
        case 'visual':
          prompt = generateVisualPrompt(request.config);
          break;
        case 'reels':
          prompt = generateReelsPrompt(request.config);
          break;
        case 'caption':
          prompt = generateCaptionPrompt(request.config);
          break;
        case 'ad':
          prompt = generateAdCopyPrompt(request.config);
          break;
        default:
          throw new Error(`Unsupported prompt type: ${request.type}`);
      }
    }

    return this.generateContent(prompt);
  }
}

// Export singleton instance
export const geminiApi = new GeminiAPI();

// Export individual functions for backward compatibility
export async function generateWithGemini(type: string, config: any): Promise<string> {
  return geminiApi.generatePromptContent({ type: type as any, config });
}
