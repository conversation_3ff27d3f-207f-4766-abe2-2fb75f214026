'use client';

import React from 'react';
import { useWorkflow } from '@/store/useWorkflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Play, 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  Copy,
  Sparkles,
  Image as ImageIcon,
  User,
  MessageSquare,
  FileText
} from 'lucide-react';
import { toast } from 'sonner';

const getNodeIcon = (type: string) => {
  switch (type) {
    case 'persona':
      return <User className="w-5 h-5" />;
    case 'image':
      return <ImageIcon className="w-5 h-5" />;
    case 'prompt':
      return <MessageSquare className="w-5 h-5" />;
    case 'caption':
      return <FileText className="w-5 h-5" />;
    case 'ad-copy':
      return <Sparkles className="w-5 h-5" />;
    default:
      return <Play className="w-5 h-5" />;
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    case 'executing':
      return <Clock className="w-5 h-5 text-blue-500 animate-spin" />;
    case 'error':
      return <AlertCircle className="w-5 h-5 text-red-500" />;
    default:
      return <Clock className="w-5 h-5 text-gray-400" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-50 border-green-200 text-green-800';
    case 'executing':
      return 'bg-blue-50 border-blue-200 text-blue-800';
    case 'error':
      return 'bg-red-50 border-red-200 text-red-800';
    default:
      return 'bg-gray-50 border-gray-200 text-gray-600';
  }
};

const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text);
  toast.success('Copied to clipboard!');
};

export default function VerticalWorkflowView() {
  const { nodes, edges, executeWorkflow, currentExecutingNode, executionComplete } = useWorkflow();

  // Sort nodes by their connections to show them in workflow order
  const sortedNodes = React.useMemo(() => {
    const nodeMap = new Map(nodes.map(node => [node.id, node]));
    const visited = new Set();
    const result = [];

    const dfs = (nodeId: string) => {
      if (visited.has(nodeId) || !nodeMap.has(nodeId)) return;
      visited.add(nodeId);
      result.push(nodeMap.get(nodeId));

      // Find connected nodes
      const connectedEdges = edges.filter(edge => edge.source === nodeId);
      connectedEdges.forEach(edge => dfs(edge.target));
    };

    // Start from the start node
    const startNode = nodes.find(node => node.data.type === 'start');
    if (startNode) {
      dfs(startNode.id);
    }

    // Add any remaining nodes
    nodes.forEach(node => {
      if (!visited.has(node.id)) {
        result.push(node);
      }
    });

    return result;
  }, [nodes, edges]);

  return (
    <div className="h-full bg-gray-50 overflow-auto">
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Workflow Execution</h2>
          <p className="text-gray-600">Follow your workflow progress step by step</p>
        </div>

        {/* Workflow Cards */}
        <div className="space-y-6">
          {sortedNodes.map((node, index) => (
            <div key={node.id} className="relative">
              {/* Connection Line */}
              {index < sortedNodes.length - 1 && (
                <div className="absolute left-1/2 transform -translate-x-1/2 top-full w-0.5 h-6 bg-gray-300 z-0"></div>
              )}

              {/* Node Card */}
              <Card className={`relative z-10 transition-all duration-300 hover:shadow-lg ${
                currentExecutingNode === node.id ? 'ring-2 ring-blue-500 shadow-lg' : ''
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${
                        node.data.type === 'start' ? 'bg-green-100 text-green-600' :
                        node.data.type === 'end' ? 'bg-purple-100 text-purple-600' :
                        'bg-blue-100 text-blue-600'
                      }`}>
                        {getNodeIcon(node.data.type)}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{node.data.title || node.data.label}</CardTitle>
                        <p className="text-sm text-gray-500 capitalize">{node.data.type}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(node.data.status)}
                      <Badge variant="outline" className={getStatusColor(node.data.status)}>
                        {node.data.status || 'pending'}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>

                <CardContent>
                  {/* Node Description */}
                  {node.data.description && (
                    <p className="text-gray-600 mb-4">{node.data.description}</p>
                  )}

                  {/* Node Configuration */}
                  {node.data.config && (
                    <div className="bg-gray-50 rounded-lg p-3 mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Configuration</h4>
                      <div className="text-sm text-gray-600">
                        {Object.entries(node.data.config).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="capitalize">{key}:</span>
                            <span className="font-medium">{String(value)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Result Display */}
                  {node.data.result && (
                    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-medium text-gray-700">Result</h4>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(node.data.result)}
                          className="h-7 px-2"
                        >
                          <Copy className="w-3 h-3 mr-1" />
                          Copy
                        </Button>
                      </div>
                      <div className="text-sm text-gray-800 whitespace-pre-wrap bg-gray-50 rounded p-3">
                        {node.data.result}
                      </div>
                      {node.data.timestamp && (
                        <p className="text-xs text-gray-500 mt-2">
                          Generated at {new Date(node.data.timestamp).toLocaleTimeString()}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Error Display */}
                  {node.data.error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                      <h4 className="text-sm font-medium text-red-700 mb-2">Error</h4>
                      <p className="text-sm text-red-600">{node.data.error}</p>
                    </div>
                  )}

                  {/* Execution Status */}
                  {currentExecutingNode === node.id && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-blue-500 animate-spin" />
                        <span className="text-sm text-blue-700 font-medium">Executing...</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ))}
        </div>

        {/* Execution Complete */}
        {executionComplete && (
          <div className="text-center py-8">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 inline-block">
              <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-green-800 mb-1">Workflow Complete!</h3>
              <p className="text-sm text-green-600">All steps have been executed successfully.</p>
            </div>
          </div>
        )}

        {/* Empty State */}
        {nodes.length === 0 && (
          <div className="text-center py-12">
            <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Workflow Created</h3>
            <p className="text-gray-600">Add some nodes to your workflow to get started.</p>
          </div>
        )}
      </div>
    </div>
  );
}
