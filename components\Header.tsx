'use client';

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { User<PERSON><PERSON><PERSON>, SignedIn, SignedOut, useUser } from "@clerk/nextjs";
import { ModeToggle } from "./ModeToggle";
import { useEffect } from "react";

export default function Header() {
  const { user } = useUser();

  useEffect(() => {
    const syncUser = async () => {
      if (user) {
        try {
          await fetch('/api/user/sync', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              id: user.id,
              email: user.primaryEmailAddress?.emailAddress,
              full_name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
              avatar_url: user.imageUrl
            }),
          });
        } catch (error) {
          console.error('Error syncing user:', error);
        }
      }
    };

    syncUser();
  }, [user]);

  return (
    <header className="border-b bg-background">
      <div className="container flex h-16 items-center justify-between px-4">
        <Link href="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold">S</span>
          </div>
          <span className="text-xl font-bold">SnaPrompt</span>
        </Link>

        <div className="flex items-center gap-4">
          <SignedIn>
            <Link href="/create">
              <Button variant="ghost">Dashboard</Button>
            </Link>
            <ModeToggle />
            <UserButton afterSignOutUrl="/" />
          </SignedIn>
          
          <SignedOut>
            <Link href="/sign-in">
              <Button variant="ghost">Sign In</Button>
            </Link>
            <Link href="/sign-up">
              <Button>Get Started</Button>
            </Link>
          </SignedOut>
        </div>
      </div>
    </header>
  );
}
