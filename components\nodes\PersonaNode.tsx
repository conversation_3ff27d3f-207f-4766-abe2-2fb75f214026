'use client';

import React, { useState } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Settings, 
  Palette,
  Target,
  Edit3,
  Check,
  X
} from 'lucide-react';
import { usePromptStore } from '@/store/usePromptStore';

interface PersonaNodeData {
  title: string;
  description?: string;
  type: 'persona';
  config?: {
    industry?: string;
    defaultTone?: string;
    suggestedKeywords?: string[];
  };
  selectedPersona?: any;
}

export default function PersonaNode({ data, selected, id }: NodeProps<PersonaNodeData>) {
  const { selectedPersona, personas } = usePromptStore();
  const [isEditing, setIsEditing] = useState(false);

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const getPersonaColor = () => {
    if (selectedPersona?.color_palette?.primary) {
      return selectedPersona.color_palette.primary;
    }
    return '#6366f1';
  };

  const getStatusColor = () => {
    if (selectedPersona) return 'bg-green-500';
    return 'bg-gray-400';
  };

  return (
    <div className="relative workflow-node group">
      <Handle
        type="source"
        position={Position.Right}
        id="persona-output"
        className="w-3 h-3 !bg-purple-500 border-2 border-white"
      />
      
      <Card 
        className={`w-80 cursor-pointer transition-all duration-200 ${
          selected ? 'ring-2 ring-purple-500 shadow-lg' : 'shadow-md hover:shadow-lg'
        }`}
        onDoubleClick={handleDoubleClick}
      >
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-3">
            <div 
              className="w-10 h-10 rounded-xl flex items-center justify-center"
              style={{ backgroundColor: getPersonaColor() }}
            >
              <User className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="text-lg font-semibold">{data.title}</span>
                <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
              </div>
              <p className="text-sm text-gray-600 font-normal">{data.description}</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setIsEditing(true);
              }}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <Edit3 className="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-3">
          {selectedPersona ? (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Palette className="w-3 h-3" />
                  {selectedPersona.industry}
                </Badge>
                <Badge variant="outline">
                  {selectedPersona.tone_of_voice}
                </Badge>
              </div>
              
              <div className="bg-gray-50 p-3 rounded-lg">
                <h4 className="font-medium text-sm mb-2">{selectedPersona.name}</h4>
                <p className="text-xs text-gray-600 mb-2">{selectedPersona.description}</p>
                
                {selectedPersona.style_keywords && (
                  <div className="flex flex-wrap gap-1">
                    {selectedPersona.style_keywords.slice(0, 4).map((keyword: string) => (
                      <Badge key={keyword} variant="secondary" className="text-xs">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex items-center gap-2 text-xs text-gray-500">
                <Target className="w-3 h-3" />
                <span>
                  {selectedPersona.target_platforms?.slice(0, 2).join(', ')}
                  {selectedPersona.target_platforms?.length > 2 && ` +${selectedPersona.target_platforms.length - 2} more`}
                </span>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <User className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-3">No persona selected</p>
              <Button 
                size="sm" 
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditing(true);
                }}
                className="bg-purple-600 hover:bg-purple-700"
              >
                Select Persona
              </Button>
            </div>
          )}

          {data.config?.suggestedKeywords && (
            <div className="border-t pt-3">
              <p className="text-xs text-gray-500 mb-2">Suggested for {data.config.industry}:</p>
              <div className="flex flex-wrap gap-1">
                {data.config.suggestedKeywords.map((keyword) => (
                  <Badge key={keyword} variant="outline" className="text-xs">
                    {keyword}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Modal/Overlay would go here */}
      {isEditing && (
        <div className="absolute inset-0 bg-white border-2 border-purple-500 rounded-lg shadow-xl z-10 p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Configure Persona</h3>
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsEditing(false)}
              >
                <Check className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsEditing(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            <p>Double-click to configure persona settings.</p>
            <p className="mt-2">Available personas: {personas.length}</p>
          </div>
        </div>
      )}
    </div>
  );
}
