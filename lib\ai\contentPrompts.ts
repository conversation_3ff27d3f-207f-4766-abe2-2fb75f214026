import { GoogleGenAI } from "@google/genai";

if (!process.env.NEXT_PUBLIC_GEMINI_API_KEY && !process.env.GEMINI_API_KEY) {
  console.error("Gemini API key is missing. Please set the NEXT_PUBLIC_GEMINI_API_KEY or GEMINI_API_KEY environment variable.");
}

const ai = new GoogleGenAI({ 
  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY || "" 
});

const model = "gemini-2.5-flash";

export interface ContentOptions {
  persona: string;
  platform: string;
  goal: string;
  tone?: string;
  length?: 'short' | 'medium' | 'long';
  includeHashtags?: boolean;
  includeEmojis?: boolean;
}

export async function generateReelsScript(options: ContentOptions): Promise<string> {
  const prompt = `
You are a Reels creative strategist for premium brands.

Brand Persona: ${options.persona}
Platform: ${options.platform}
Goal: ${options.goal}
Tone: ${options.tone || 'brand-appropriate'}

Generate a short, high-impact Reels script idea (3-7 bullet points) tailored to the brand style and platform best practices.

Include:
- Hook (first 3 seconds)
- Main content points
- Call to action
- Visual direction notes
- Trending elements when appropriate

Format as a clear, actionable script.`;

  try {
    const response = await ai.models.generateContent({
      model,
      contents: [{ text: prompt }],
    });

    return response.text.trim();
  } catch (error) {
    console.error("Error generating Reels script:", error);
    throw new Error("Failed to generate Reels script. Please try again.");
  }
}

export async function generateInstagramCaption(options: ContentOptions): Promise<string[]> {
  const prompt = `
Act like a brand copywriter for a ${options.persona} brand with a "${options.tone || 'engaging'}" voice.

Platform: ${options.platform}
Goal: ${options.goal}
Length: ${options.length || 'medium'}

Write 3 Instagram captions that include:
${options.includeEmojis ? '- Strategic emoji usage (not overwhelming)' : '- No emojis'}
- Engaging hooks or compelling opening lines
- Value-driven content
- Clear call to action
${options.includeHashtags ? '- Relevant hashtags (5-10)' : '- No hashtags'}
- Brand voice consistency

Each caption should be distinct in approach but maintain brand consistency.

Return each caption separated by "---" on its own line.`;

  try {
    const response = await ai.models.generateContent({
      model,
      contents: [{ text: prompt }],
    });

    const captions = response.text.trim()
      .split('---')
      .map(caption => caption.trim())
      .filter(caption => caption.length > 0);

    return captions.length >= 3 ? captions.slice(0, 3) : captions;
  } catch (error) {
    console.error("Error generating Instagram captions:", error);
    throw new Error("Failed to generate captions. Please try again.");
  }
}

export async function generateAdCopy(options: ContentOptions): Promise<string> {
  const prompt = `
You are an expert ad copywriter specializing in ${options.platform} advertising.

Brand Persona: ${options.persona}
Platform: ${options.platform}
Campaign Goal: ${options.goal}
Tone: ${options.tone || 'persuasive'}

Create compelling ad copy that includes:
- Attention-grabbing headline
- Persuasive body text
- Strong call to action
- Platform-specific optimizations
- Emotional triggers appropriate for the persona

Focus on conversion and engagement while maintaining brand authenticity.

Format as a complete ad with clear sections.`;

  try {
    const response = await ai.models.generateContent({
      model,
      contents: [{ text: prompt }],
    });

    return response.text.trim();
  } catch (error) {
    console.error("Error generating ad copy:", error);
    throw new Error("Failed to generate ad copy. Please try again.");
  }
}

export async function generateHashtags(
  content: string, 
  persona: string, 
  platform: string = "instagram",
  count: number = 10
): Promise<string[]> {
  const prompt = `
You are a social media hashtag strategist.

Content: "${content}"
Brand Persona: ${persona}
Platform: ${platform}

Generate ${count} relevant hashtags that:
- Mix popular and niche tags
- Are relevant to the content and brand
- Include branded hashtags when appropriate
- Follow ${platform} best practices
- Balance reach and engagement potential

Return only the hashtags, one per line, with # symbol.`;

  try {
    const response = await ai.models.generateContent({
      model,
      contents: [{ text: prompt }],
    });

    const hashtags = response.text.trim()
      .split('\n')
      .map(tag => tag.trim())
      .filter(tag => tag.startsWith('#') && tag.length > 1)
      .slice(0, count);

    return hashtags;
  } catch (error) {
    console.error("Error generating hashtags:", error);
    return [`#${persona.toLowerCase().replace(/\s+/g, '')}`, '#content', '#brand'];
  }
}

export async function generateContentIdeas(
  persona: string, 
  platform: string, 
  count: number = 5
): Promise<string[]> {
  const prompt = `
You are a content strategist for ${persona} brands.

Generate ${count} creative content ideas for ${platform} that:
- Align with the ${persona} brand persona
- Are platform-appropriate
- Drive engagement and brand awareness
- Include variety (educational, entertaining, promotional)
- Are actionable and specific

Return each idea as a brief, clear description (1-2 sentences).`;

  try {
    const response = await ai.models.generateContent({
      model,
      contents: [{ text: prompt }],
    });

    const ideas = response.text.trim()
      .split('\n')
      .map(idea => idea.replace(/^\d+\.\s*/, '').trim())
      .filter(idea => idea.length > 0)
      .slice(0, count);

    return ideas;
  } catch (error) {
    console.error("Error generating content ideas:", error);
    throw new Error("Failed to generate content ideas. Please try again.");
  }
}
