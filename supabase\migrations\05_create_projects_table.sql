-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  canvas_data JSONB DEFAULT '{}',
  created_by TEXT REFERENCES users(id) ON DELETE CASCADE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Create function to get current user ID from Clerk
CREATE OR REPLACE FUNCTION get_clerk_user_id()
RETURNS TEXT AS $$
BEGIN
  RETURN current_setting('request.headers.x-clerk-user-id', true);
EXCEPTION
  WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create policies
-- 1. Users can read their own projects
CREATE POLICY "Users can view own projects"
ON projects
FOR SELECT
USING (created_by = get_clerk_user_id());

-- 2. Users can create projects
CREATE POLICY "Users can create projects"
ON projects
FOR INSERT
WITH CHECK (created_by = get_clerk_user_id());

-- 3. Users can update their own projects
CREATE POLICY "Users can update own projects"
ON projects
FOR UPDATE
USING (created_by = get_clerk_user_id());

-- 4. Users can delete their own projects
CREATE POLICY "Users can delete own projects"
ON projects
FOR DELETE
USING (created_by = get_clerk_user_id());

-- Create indexes
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_updated_at ON projects(updated_at);

-- Grant access to authenticated users
GRANT ALL ON projects TO authenticated; 