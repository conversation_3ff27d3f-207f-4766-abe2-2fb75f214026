import { Inter } from "next/font/google";
import "./globals.css";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import ClientLayout from "./ClientLayout";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "SnaPrompt - AI-Powered Brand Prompt Generator",
  description: "Transform your brand photos into high-conversion prompts using AI",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <body className={inter.className}>
          <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
    </ClerkProvider>
  );
}
