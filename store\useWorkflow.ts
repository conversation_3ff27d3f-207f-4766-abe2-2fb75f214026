import { create } from 'zustand';
import { <PERSON>, Edge, Node, OnNodesChange, OnEdgesChange, applyNodeChang<PERSON>, applyEdgeChanges, XYPosition } from 'reactflow';
import { useProjectStore } from '@/lib/store/projects';

interface WorkflowState {
  nodes: Node[];
  edges: Edge[];
  currentExecutingNode: string | null;
  executionComplete: boolean;
  suggestions: Record<string, any[]>;
  workflowOptimizations: string[];
  isLoadingSuggestions: boolean;
  collapsedSuggestions: Record<string, boolean>;
  onNodesChange: OnNodesChange;
  onEdgesChange: OnEdgesChange;
  addNode: (node: Node) => void;
  deleteNode: (nodeId: string) => void;
  onConnect: (connection: Connection) => void;
  executeWorkflow: () => void;
  toggleSuggestions: (nodeId: string) => void;
  fetchAISuggestions: (nodeId: string, description: string) => void;
  fetchWorkflowOptimizations: () => void;
  clearAll: () => void;
  saveWorkflow: (userId: string, name: string, description?: string) => Promise<void>;
  loadWorkflow: (canvasData: any) => void;
  setNodes: (nodes: Node[]) => void;
  setEdges: (edges: Edge[]) => void;
  createWorkflowFromConfig: (config: any) => void;
}

const calculateNewPosition = (): XYPosition => {
  return {
    x: Math.random() * 300 + 100,
    y: Math.random() * 300 + 100,
  };
};

const createNodeWithDefaults = (node: Node) => ({
  ...node,
  draggable: true,
  style: {
    ...node.style,
    transition: 'all 0.3s ease',
  },
});

export const useWorkflow = create<WorkflowState>((set, get) => ({
  nodes: [
    createNodeWithDefaults({
      id: 'start',
      type: 'task',
      data: { title: 'Start', type: 'start' },
      position: { x: 100, y: 100 },
    }),
  ],
  edges: [],
  currentExecutingNode: null,
  executionComplete: false,
  suggestions: {},
  workflowOptimizations: [],
  isLoadingSuggestions: false,
  collapsedSuggestions: {},

  onNodesChange: (changes) => {
    set({
      nodes: applyNodeChanges(changes, get().nodes).map(node => ({
        ...node,
        style: {
          ...node.style,
          transition: 'all 0.3s ease',
        },
      })),
    });
  },

  onEdgesChange: (changes) => {
    set({
      edges: applyEdgeChanges(changes, get().edges),
    });
  },

  addNode: (node) => {
    const position = calculateNewPosition();
    set((state) => ({
      nodes: [...state.nodes, createNodeWithDefaults({ ...node, position })],
    }));
  },

  deleteNode: (nodeId) => {
    set((state) => ({
      nodes: state.nodes.filter((n) => n.id !== nodeId),
      edges: state.edges.filter(
        (e) => e.source !== nodeId && e.target !== nodeId
      ),
    }));
  },

  onConnect: (connection) => {
    if (!connection.source || !connection.target) return;
    
    const newEdge: Edge = {
      id: `${connection.source}-${connection.target}`,
      source: connection.source,
      target: connection.target,
    };

    set((state) => ({
      edges: [...state.edges, newEdge],
    }));
  },

  executeWorkflow: async () => {
    const state = get();
    const { nodes, edges } = state;

    if (nodes.length === 0) return;

    // Reset execution state
    set({ currentExecutingNode: null, executionComplete: false });

    // Find start node
    const startNode = nodes.find(node => node.data.type === 'start');
    if (!startNode) {
      console.error('No start node found');
      return;
    }

    // Execute workflow step by step
    await executeWorkflowSequentially(startNode.id, get, set);
  },

  toggleSuggestions: (nodeId) => {
    set((state) => ({
      collapsedSuggestions: {
        ...state.collapsedSuggestions,
        [nodeId]: !state.collapsedSuggestions[nodeId],
      },
    }));
  },

  fetchAISuggestions: async (nodeId: string, description: string) => {
    set({ isLoadingSuggestions: true });
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    set((state) => ({
      isLoadingSuggestions: false,
      suggestions: {
        ...state.suggestions,
        [nodeId]: [
          {
            name: 'ChatGPT',
            category: 'AI Assistant',
            description: 'AI-powered content generation and brainstorming',
            url: 'https://chat.openai.com',
          },
          {
            name: 'Midjourney',
            category: 'Image Generation',
            description: 'Create stunning visuals from text descriptions',
            url: 'https://midjourney.com',
          },
        ],
      },
    }));
  },

  fetchWorkflowOptimizations: async () => {
    set({ isLoadingSuggestions: true });
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    set({
      isLoadingSuggestions: false,
      workflowOptimizations: [
        'Consider adding a review step between content creation and publishing',
        'Automate image optimization using AI tools',
        'Implement parallel processing for independent tasks',
      ],
    });
  },

  clearAll: () => {
    set({
      nodes: [
        createNodeWithDefaults({
          id: 'start',
          type: 'task',
          data: { title: 'Start', type: 'start' },
          position: { x: 100, y: 100 },
        }),
      ],
      edges: [],
      currentExecutingNode: null,
      executionComplete: false,
      suggestions: {},
      workflowOptimizations: [],
    });
  },

  saveWorkflow: async (userId: string, name: string, description?: string) => {
    if (!userId) {
      throw new Error('User ID is required to save workflow');
    }

    try {
      const { nodes, edges } = get();
      const canvasData = {
        nodes: nodes.map(node => ({
          ...node,
          // Remove any circular references or non-serializable data
          data: {
            ...node.data,
            // Add any specific data cleanup here if needed
          }
        })),
        edges: edges.map(edge => ({
          ...edge,
          // Clean up edge data if needed
        }))
      };
      
      const result = await useProjectStore.getState().createProject(userId, name, description, canvasData);
      return result;
    } catch (error) {
      console.error('Error saving workflow:', error);
      throw error;
    }
  },

  loadWorkflow: (canvasData: any) => {
    if (canvasData?.nodes && canvasData?.edges) {
      set({
        nodes: canvasData.nodes,
        edges: canvasData.edges,
        currentExecutingNode: null,
        executionComplete: false,
        suggestions: {},
        workflowOptimizations: [],
      });
    }
  },

  setNodes: (nodesOrUpdater: Node[] | ((prevNodes: Node[]) => Node[])) => {
    if (typeof nodesOrUpdater === 'function') {
      set((state) => ({
        nodes: nodesOrUpdater(state.nodes).map(createNodeWithDefaults)
      }));
    } else {
      set({ nodes: nodesOrUpdater.map(createNodeWithDefaults) });
    }
  },

  setEdges: (edges: Edge[]) => {
    set({ edges });
  },

  createWorkflowFromConfig: (config: any) => {
    const { generateWorkflowFromConfig } = require('@/lib/workflowAutomation');
    const { nodes: newNodes, edges: newEdges } = generateWorkflowFromConfig(config);
    set({
      nodes: newNodes.map(createNodeWithDefaults),
      edges: newEdges,
      currentExecutingNode: null,
      executionComplete: false,
      suggestions: {},
      workflowOptimizations: []
    });
  },
}));

// Helper function for sequential workflow execution
async function executeWorkflowSequentially(
  currentNodeId: string,
  get: () => WorkflowState,
  set: (partial: Partial<WorkflowState>) => void
) {
  const { executeWorkflowStep } = await import('@/lib/workflowAutomation');
  const state = get();
  const { nodes, edges } = state;

  const currentNode = nodes.find(node => node.id === currentNodeId);
  if (!currentNode) return;

  // Update node status to executing
  set({
    nodes: nodes.map(node =>
      node.id === currentNodeId
        ? { ...node, data: { ...node.data, status: 'executing' } }
        : node
    ),
    currentExecutingNode: currentNodeId
  });

  // Add a small delay to show the executing state
  await new Promise(resolve => setTimeout(resolve, 300));

  // Execute the node if it's a prompt type
  if (currentNode.data.type !== 'start' && currentNode.data.type !== 'end') {
    try {
      const result = await executeWorkflowStep(currentNodeId, currentNode.data);

      // Update node with result
      const updatedState = get();
      set({
        nodes: updatedState.nodes.map(node =>
          node.id === currentNodeId
            ? {
                ...node,
                data: {
                  ...node.data,
                  status: 'completed',
                  result: result.result,
                  timestamp: result.timestamp
                }
              }
            : node
        )
      });
    } catch (error) {
      // Update node with error
      const updatedState = get();
      set({
        nodes: updatedState.nodes.map(node =>
          node.id === currentNodeId
            ? {
                ...node,
                data: {
                  ...node.data,
                  status: 'error',
                  error: 'Failed to execute step'
                }
              }
            : node
        )
      });
      return;
    }
  } else {
    // For start/end nodes, just mark as completed
    const updatedState = get();
    set({
      nodes: updatedState.nodes.map(node =>
        node.id === currentNodeId
          ? { ...node, data: { ...node.data, status: 'completed' } }
          : node
      )
    });
  }

  // Find next nodes
  const currentState = get();
  const nextEdges = currentState.edges.filter(edge => edge.source === currentNodeId);

  // Execute next nodes (sequential execution)
  for (const edge of nextEdges) {
    await executeWorkflowSequentially(edge.target, get, set);
  }

  // If this is the last node, mark workflow as complete
  if (nextEdges.length === 0) {
    set({
      currentExecutingNode: null,
      executionComplete: true
    });
  }
}

export default useWorkflow;
