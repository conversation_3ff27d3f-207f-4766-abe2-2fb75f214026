Now let’s adapt your current Gemini integration pattern (used for ingredient analysis) to build your core brand workflow features for your app like:

Persona analysis

Vision/image prompt generation

<PERSON><PERSON> script prompt

Caption generator

Ad copy prompt

Prompt improvement loop

Below is a clean base structure you can extend node-by-node (as per your visual workflow UI) using modular Gemini functions, just like you did for OCR.

🔧 File Structure (Recommended)
bash
Copy
Edit
/lib/ai/
  └── personaPrompt.ts
  └── imageToPrompt.ts
  └── reelsPrompt.ts
  └── captionPrompt.ts
  └── promptImprover.ts
  └── utils.ts
✅ 1. personaPrompt.ts
ts
Copy
Edit
import { GoogleGenAI } from "@google/genai";
const ai = new GoogleGenAI({ apiKey: process.env.API_KEY! });

const model = "gemini-2.5-flash-preview-04-17";

export async function generatePersonaStyle(persona: string) {
  const prompt = `
You are an expert brand strategist and visual director.

Given a brand persona like "${persona}", describe:
- Visual style (e.g., minimal, bold, luxury)
- Preferred color palette
- Typical tone of voice (e.g., soft, confident, playful)
- Ideal platforms (e.g., Instagram, Pinterest, Reels)

Return ONLY in clean JSON like this:
{
  "style": "...",
  "palette": "...",
  "tone": "...",
  "platforms": ["..."]
}`;

  const response = await ai.models.generateContent({
    model,
    contents: [{ text: prompt }],
  });

  return JSON.parse(response.text);
}
✅ 2. imageToPrompt.ts (Like Your Ingredient OCR)
ts
Copy
Edit
export async function generateVisualPrompt(base64Image: string, mimeType: string, persona: string) {
  const imagePart = {
    inlineData: { mimeType, data: base64Image },
  };

  const textPart = {
    text: `You're a top-tier AI prompt engineer and creative director.

Given this reference image and the brand persona "${persona}", generate a visual storytelling prompt for Midjourney. Style should match the brand tone and aesthetic. Only output the prompt string.`,
  };

  const response = await ai.models.generateContent({
    model,
    contents: { parts: [imagePart, textPart] },
  });

  return response.text.trim();
}
✅ 3. reelsPrompt.ts
ts
Copy
Edit
export async function generateReelsScriptPrompt(goal: string, persona: string) {
  const prompt = `
You are a Reels creative strategist for premium brands.

Persona: ${persona}
Goal: ${goal}

Generate a short, high-impact Reels script idea (3-5 bullet points) tailored to the brand style and platform norms.`;

  const response = await ai.models.generateContent({
    model,
    contents: [{ text: prompt }],
  });

  return response.text.trim();
}
✅ 4. captionPrompt.ts
ts
Copy
Edit
export async function generateInstagramCaption(productType: string, tone: string) {
  const prompt = `
Act like a brand copywriter for a ${productType} product with a "${tone}" voice.

Write 3 Instagram captions with:
- Emojis (light usage)
- Hooks or poetic lines
- 150 characters max

Only return the captions, no explanation.`;

  const response = await ai.models.generateContent({
    model,
    contents: [{ text: prompt }],
  });

  return response.text.trim().split("\n").filter((line) => line.trim() !== "");
}
✅ 5. promptImprover.ts
ts
Copy
Edit
export async function improvePrompt(rawPrompt: string) {
  const prompt = `
You are an expert prompt engineer for Midjourney v6 and SDXL.

Here is a raw prompt:  
"${rawPrompt}"

Improve it by:
- Making it more descriptive
- Adding camera details if missing
- Enhancing realism or aesthetic appeal
- Optimizing aspect ratio + model params

Return only the improved prompt.`;

  const response = await ai.models.generateContent({
    model,
    contents: [{ text: prompt }],
  });

  return response.text.trim();
}
🧠 Notes for Integration in Cursor
Use Zustand or Context to pass persona + goal across nodes

Image upload component should return base64 + mimeType

Trigger Gemini API on “run” click for each prompt node

Store final prompt in Supabase with timestamp/user

now i want to tell cursor to build my core feature like persona image vision etc with google gemini api 



import { GoogleGenAI } from "@google/genai";

import type { IngredientAnalysis } from '../types';



if (!process.env.API_KEY) {

    console.error("API key is missing. Please set the API_KEY environment variable.");

}



const ai = new GoogleGenAI({ apiKey: process.env.API_KEY! });



const ocrModel = "gemini-2.5-flash-preview-04-17";

const analysisModel = "gemini-2.5-flash-preview-04-17";



/**

 * Extracts text from a given image using Gemini.

 * @param base64Image The base64 encoded image string.

 * @param mimeType The MIME type of the image.

 * @returns The extracted text as a string.

 */

export async function extractTextFromImage(base64Image: string, mimeType: string): Promise<string> {

    try {

        const imagePart = {

            inlineData: {

                mimeType: mimeType,

                data: base64Image,

            },

        };



        const textPart = {

            text: "Extract the text from this image of a food ingredient list. Focus only on the ingredients themselves. Ignore nutritional facts tables, marketing text, or other non-ingredient information."

        };



        const response = await ai.models.generateContent({

            model: ocrModel,

            contents: { parts: [imagePart, textPart] },

        });



        return response.text;

    } catch (error) {

        console.error("Error extracting text from image:", error);

        throw new Error("Failed to process image with Gemini API.");

    }

}



/**

 * Analyzes a list of ingredients using Gemini.

 * @param ingredientsText The string of ingredients to analyze.

 * @returns A structured analysis of the ingredients.

 */

export async function analyzeIngredients(ingredientsText: string): Promise<IngredientAnalysis> {

    const systemInstruction = `You are an expert food scientist and nutritionist assistant. Your task is to analyze a list of food ingredients and provide a structured, factual analysis. For each ingredient, classify it into one of the following categories: 'Beneficial', 'Neutral', 'Controversial', or 'Potentially Harmful'. Provide a brief, neutral reason for your classification. Do not use markdown formatting in your reasons.`;



    const prompt = `

Analyze the following list of ingredients. Please provide the output ONLY in JSON format, following this exact structure:

{

  "summary": "A brief, one-sentence overview of the product's ingredient profile.",

  "ingredients": [

    {

      "name": "Ingredient Name",

      "classification": "One of: Beneficial, Neutral, Controversial, Potentially Harmful",

      "reason": "A brief, factual explanation for the classification. Mention common concerns, allergens, or benefits.",

      "source": "A URL to a reputable source (e.g., FDA, NIH, Healthline, scientific study) if available."

    }

  ],

  "disclaimer": "This analysis is for informational purposes only and is not medical advice. Consult with a healthcare professional for any health concerns."

}



Ingredient List:

[${ingredientsText}]

`;



    try {

        const response = await ai.models.generateContent({

            model: analysisModel,

            contents: prompt,

            config: {

                systemInstruction,

                responseMimeType: "application/json",

            }

        });



        let jsonStr = response.text.trim();

        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;

        const match = jsonStr.match(fenceRegex);

        if (match && match[2]) {

            jsonStr = match[2].trim();

        }



        try {

            const parsedData: IngredientAnalysis = JSON.parse(jsonStr);

            return parsedData;

        } catch (e) {

            console.error("Failed to parse JSON response:", e, "Raw response:", jsonStr);

            throw new Error("The AI returned a response that was not valid JSON. Please try again.");

        }

    } catch (error) {

        console.error("Error analyzing ingredients with Gemini:", error);

        throw new Error("Failed to get analysis from Gemini API.");

    }

}

like this also for different there will be differernt fine tuining of prompt so that it should give best result ok