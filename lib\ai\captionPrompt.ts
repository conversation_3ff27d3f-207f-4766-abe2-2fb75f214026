export interface CaptionConfig {
  persona: any;
  platform: string;
  mood: string;
  description: string;
  postType?: string;
  includeHashtags?: boolean;
  includeEmojis?: boolean;
}

export function generateCaptionPrompt(config: CaptionConfig): string {
  const { persona, platform, mood, description, postType = 'general', includeHashtags = true, includeEmojis = true } = config;

  const platformSpecifics = {
    instagram: {
      maxLength: '2200 characters',
      features: 'hashtags (up to 30), mentions, emojis',
      style: 'visual storytelling, lifestyle-focused',
      engagement: 'questions, polls, story prompts'
    },
    facebook: {
      maxLength: '63,206 characters',
      features: 'longer form content, links, mentions',
      style: 'conversational, community-building',
      engagement: 'discussions, shares, reactions'
    },
    linkedin: {
      maxLength: '3000 characters',
      features: 'professional tone, industry insights',
      style: 'thought leadership, business-focused',
      engagement: 'professional discussions, networking'
    },
    twitter: {
      maxLength: '280 characters',
      features: 'concise, hashtags, mentions, threads',
      style: 'quick insights, news, conversations',
      engagement: 'retweets, replies, trending topics'
    },
    tiktok: {
      maxLength: '2200 characters',
      features: 'trending hashtags, challenges, sounds',
      style: 'casual, fun, authentic',
      engagement: 'challenges, duets, comments'
    }
  };

  const currentPlatform = platformSpecifics[platform as keyof typeof platformSpecifics] || platformSpecifics.instagram;

  return `Create an engaging ${platform} caption for ${persona.name || 'the brand'}.

**Content Brief:**
${description}

**Brand Voice:** ${persona.description || 'Professional and engaging'}
**Mood/Tone:** ${mood}
**Platform:** ${platform.charAt(0).toUpperCase() + platform.slice(1)}
**Post Type:** ${postType}

**Platform Guidelines:**
- Maximum length: ${currentPlatform.maxLength}
- Style: ${currentPlatform.style}
- Features: ${currentPlatform.features}
- Engagement strategy: ${currentPlatform.engagement}

**Caption Structure:**

**HOOK (First Line):**
- Create an attention-grabbing opening
- Make people want to read more
- Use ${mood} tone that matches brand voice

**MAIN CONTENT:**
- Deliver the core message clearly
- Tell a story or share valuable insights
- Maintain ${persona.name || 'brand'} personality
- Include relevant details about: ${description}

**ENGAGEMENT ELEMENT:**
- Ask a question or prompt interaction
- Encourage ${currentPlatform.engagement}
- Create community conversation

**CALL-TO-ACTION:**
- Clear next step for audience
- Align with brand goals
- Platform-appropriate action

${includeHashtags ? `**HASHTAGS:**
- Include relevant, trending hashtags
- Mix of popular and niche tags
- Platform-specific hashtag strategy
- Research current trending tags for ${platform}` : ''}

${includeEmojis ? `**EMOJIS:**
- Use strategically to enhance readability
- Match ${mood} tone and brand personality
- Don't overuse - maintain professionalism` : ''}

**Additional Requirements:**
- Optimize for ${platform} algorithm
- Ensure accessibility (alt text considerations)
- Match ${persona.name || 'brand'} established voice and values
- Include relevant keywords naturally
- Create shareable, engaging content

Generate a complete, ready-to-post caption that drives engagement and aligns with brand goals.`;
}

export function generateCaptionWithGemini(config: CaptionConfig): Promise<string> {
  const prompt = generateCaptionPrompt(config);
  
  // This will be replaced with actual Gemini API call
  return new Promise((resolve) => {
    setTimeout(() => {
      const hashtagsSection = config.includeHashtags ? `

#${config.platform} #contentcreator #brandstory #engagement #socialmedia #marketing #${config.mood} #trending #community #growth` : '';

      const emojisToUse = config.includeEmojis ? ['✨', '💫', '🚀', '💡', '🎯', '📈', '🔥', '💪'] : [];
      const randomEmoji = emojisToUse[Math.floor(Math.random() * emojisToUse.length)];

      resolve(`${config.includeEmojis ? randomEmoji + ' ' : ''}Ready to transform your ${config.platform} game? 

Here's what we've learned about ${config.description.toLowerCase()}...

${config.mood === 'professional' ? 'Our research shows' : 'We discovered'} that the key to success lies in understanding your audience and delivering value consistently.

${config.includeEmojis ? '💭 ' : ''}What's your biggest challenge with ${config.platform} content? Drop a comment below - we read every single one!

${config.includeEmojis ? '👆 ' : ''}Follow @${config.persona.name || 'yourbrand'} for more insights like this.${hashtagsSection}`);
    }, 2000);
  });
}
