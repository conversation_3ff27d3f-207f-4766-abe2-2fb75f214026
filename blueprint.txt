# SnaPrompt - Complete Development Plan

## 🎯 Project Overview

**Product Name**: SnaPrompt
**Product Type**: SaaS Platform - Canvas-based AI Prompt Generator
**Target Market**: Brand marketing teams, agencies, creative freelancers
**Core Value Proposition**: Transform brand images into high-conversion prompts using visual workflow interface

### Key Features
- **Visual Canvas Workflow**: Drag-and-drop interface for prompt creation
- **Brand Persona Intelligence**: Industry-specific prompt optimization
- **Multi-Platform Output**: Midjourney, ChatGPT, Firefly prompts
- **Vision AI Integration**: Automatic image analysis and tagging
- **Team Collaboration**: Workspace management and sharing
- **Template Marketplace**: Pre-built prompt templates

---

## 📋 Development Timeline & Phases

### **Phase 1: MVP Foundation (Weeks 1-4)**
**Goal**: Basic working prototype with core features

#### Week 1: Project Setup & Architecture
- [ ] Initialize Next.js 14 project with TypeScript
- [ ] Set up Supabase database
- [ ] Configure Clerk authentication
- [ ] Create basic project structure
- [ ] Set up development environment

#### Week 2: Database Design & Setup
- [ ] Design complete database schema
- [ ] Set up Prisma ORM
- [ ] Create database migrations
- [ ] Set up seed data for testing
- [ ] Configure database relationships

#### Week 3: Authentication & User Management
- [ ] Implement Clerk authentication
- [ ] Create user dashboard
- [ ] Set up workspace management
- [ ] Build user profile system
- [ ] Configure authorization middleware

#### Week 4: Basic Canvas Interface
- [ ] Install React Flow library
- [ ] Create basic canvas component
- [ ] Build foundational node types
- [ ] Implement node connections
- [ ] Add canvas save/load functionality

### **Phase 2: Core Features (Weeks 5-8)**
**Goal**: Implement main prompt generation features

#### Week 5: Brand Persona System
- [ ] Create brand persona database structure
- [ ] Build persona selection interface
- [ ] Implement persona customization
- [ ] Create persona templates (Skincare, Fashion, etc.)
- [ ] Add persona validation logic

#### Week 6: Image Upload & Analysis
- [ ] Set up file upload system (AWS S3/Supabase)
- [ ] Integrate Google Vision API
- [ ] Build image analysis pipeline
- [ ] Create image preview components
- [ ] Implement analysis result display

#### Week 7: AI Prompt Generation
- [ ] Set up OpenAI API integration
- [ ] Create prompt generation logic
- [ ] Build prompt template system
- [ ] Implement multi-platform output
- [ ] Add prompt customization options

#### Week 8: Canvas Node System
- [ ] Build PersonaNode component
- [ ] Create ImageUploadNode component
- [ ] Implement GoalSelectionNode
- [ ] Build PromptOutputNode
- [ ] Add node interaction logic

### **Phase 3: Advanced Features (Weeks 9-12)**
**Goal**: Enhanced functionality and user experience

#### Week 9: Project Management
- [ ] Create project save/load system
- [ ] Build project dashboard
- [ ] Implement project sharing
- [ ] Add project templates
- [ ] Create project history tracking

#### Week 10: Workspace & Collaboration
- [ ] Build team workspace features
- [ ] Implement user invitation system
- [ ] Create role-based permissions
- [ ] Add real-time collaboration
- [ ] Build activity feeds

#### Week 11: Advanced AI Features
- [ ] Implement prompt optimization
- [ ] Add batch prompt generation
- [ ] Create prompt variation system
- [ ] Build performance analytics
- [ ] Add A/B testing for prompts

#### Week 12: Template Marketplace
- [ ] Create template submission system
- [ ] Build template browsing interface
- [ ] Implement template rating system
- [ ] Add template search functionality
- [ ] Create template categories

### **Phase 4: SaaS Infrastructure (Weeks 13-16)**
**Goal**: Complete SaaS platform with billing

#### Week 13: Subscription System
- [ ] Set up Stripe integration
- [ ] Create pricing plans
- [ ] Build subscription management
- [ ] Implement usage tracking
- [ ] Add credit system

#### Week 14: Billing & Payment
- [ ] Create checkout flow
- [ ] Build billing dashboard
- [ ] Implement webhook handling
- [ ] Add invoice generation
- [ ] Create usage analytics

#### Week 15: Admin & Analytics
- [ ] Build admin dashboard
- [ ] Create user analytics
- [ ] Implement performance monitoring
- [ ] Add business metrics tracking
- [ ] Create reporting system

#### Week 16: Testing & Optimization
- [ ] Comprehensive testing suite
- [ ] Performance optimization
- [ ] Security audit
- [ ] Bug fixes and polish
- [ ] Documentation completion

---

## 🗄️ Database Schema Design

### Core Tables

```sql
-- Users table (managed by Clerk)
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  clerk_id VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255),
  avatar_url TEXT,
  plan_type VARCHAR(50) DEFAULT 'free',
  credits_remaining INTEGER DEFAULT 10,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Workspaces (team/organization level)
CREATE TABLE workspaces (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  owner_id UUID REFERENCES users(id),
  plan_type VARCHAR(50) DEFAULT 'creator',
  credits_remaining INTEGER DEFAULT 100,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Workspace members (team collaboration)
CREATE TABLE workspace_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  role VARCHAR(50) DEFAULT 'member', -- 'owner', 'admin', 'member'
  permissions JSONB DEFAULT '{}',
  joined_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(workspace_id, user_id)
);

-- Brand personas (reusable brand profiles)
CREATE TABLE brand_personas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  brand_type VARCHAR(100) NOT NULL, -- 'skincare', 'fashion', 'luxury', etc.
  tone VARCHAR(100), -- 'minimal', 'bold', 'vintage', etc.
  description TEXT,
  color_palette JSONB, -- Array of hex colors
  target_audience TEXT,
  brand_values JSONB, -- Array of values
  visual_style JSONB, -- Style preferences
  is_template BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Projects (individual canvas workflows)
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  canvas_data JSONB, -- Complete canvas state (nodes, edges, positions)
  thumbnail_url TEXT,
  status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'active', 'archived'
  created_by UUID REFERENCES users(id),
  last_modified_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Reference images (uploaded by users)
CREATE TABLE reference_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  file_url TEXT NOT NULL,
  file_name VARCHAR(255),
  file_size INTEGER,
  mime_type VARCHAR(100),
  vision_analysis JSONB, -- Google Vision API results
  dominant_colors JSONB, -- Array of color analysis
  detected_objects JSONB, -- Array of detected objects
  scene_analysis JSONB, -- Scene and mood analysis
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Generated prompts (AI outputs)
CREATE TABLE generated_prompts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  persona_id UUID REFERENCES brand_personas(id),
  prompt_type VARCHAR(100) NOT NULL, -- 'midjourney', 'chatgpt', 'firefly', etc.
  platform VARCHAR(100), -- 'instagram', 'pinterest', 'ads', etc.
  prompt_text TEXT NOT NULL,
  prompt_parameters JSONB, -- Additional parameters used
  input_data JSONB, -- Input data that generated this prompt
  quality_score FLOAT, -- AI-generated quality score
  used_credits INTEGER DEFAULT 1,
  is_favorited BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Prompt templates (marketplace items)
CREATE TABLE prompt_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100), -- 'photoshoot', 'social', 'ads', etc.
  brand_type VARCHAR(100), -- 'skincare', 'fashion', etc.
  template_data JSONB, -- Complete template structure
  preview_images JSONB, -- Array of preview image URLs
  tags JSONB, -- Array of searchable tags
  created_by UUID REFERENCES users(id),
  is_public BOOLEAN DEFAULT FALSE,
  is_featured BOOLEAN DEFAULT FALSE,
  download_count INTEGER DEFAULT 0,
  rating_average FLOAT DEFAULT 0,
  rating_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Template ratings (marketplace feedback)
CREATE TABLE template_ratings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id UUID REFERENCES prompt_templates(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  review TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(template_id, user_id)
);

-- Subscriptions (billing)
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  stripe_subscription_id VARCHAR(255) UNIQUE,
  stripe_customer_id VARCHAR(255),
  plan_type VARCHAR(50) NOT NULL, -- 'creator', 'brand', 'agency'
  status VARCHAR(50) NOT NULL, -- 'active', 'canceled', 'past_due'
  current_period_start TIMESTAMP,
  current_period_end TIMESTAMP,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Usage tracking (analytics & billing)
CREATE TABLE usage_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  workspace_id UUID REFERENCES workspaces(id),
  action VARCHAR(100) NOT NULL, -- 'prompt_generated', 'image_analyzed', 'template_used'
  resource_type VARCHAR(100), -- 'prompt', 'image', 'template'
  resource_id UUID,
  credits_used INTEGER DEFAULT 1,
  metadata JSONB, -- Additional context data
  created_at TIMESTAMP DEFAULT NOW()
);

-- Project collaborators (sharing)
CREATE TABLE project_collaborators (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  permission VARCHAR(50) DEFAULT 'view', -- 'view', 'edit', 'admin'
  invited_by UUID REFERENCES users(id),
  invited_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(project_id, user_id)
);

-- Activity feed (collaboration & history)
CREATE TABLE activities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id),
  action VARCHAR(100) NOT NULL, -- 'project_created', 'prompt_generated', etc.
  resource_type VARCHAR(100), -- 'project', 'prompt', 'template'
  resource_id UUID,
  metadata JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Indexes for Performance

```sql
-- Performance indexes
CREATE INDEX idx_users_clerk_id ON users(clerk_id);
CREATE INDEX idx_workspaces_owner_id ON workspaces(owner_id);
CREATE INDEX idx_projects_workspace_id ON projects(workspace_id);
CREATE INDEX idx_prompts_project_id ON generated_prompts(project_id);
CREATE INDEX idx_usage_logs_workspace_id ON usage_logs(workspace_id);
CREATE INDEX idx_activities_workspace_id ON activities(workspace_id);
CREATE INDEX idx_templates_category ON prompt_templates(category);
CREATE INDEX idx_templates_brand_type ON prompt_templates(brand_type);
```

---

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Styling**: Tailwind CSS + ShadCN/UI
- **State Management**: Zustand
- **Canvas**: React Flow
- **Forms**: React Hook Form + Zod
- **File Upload**: Uploadthing
- **Icons**: Lucide React

### Backend
- **Runtime**: Node.js
- **API**: Next.js API Routes
- **Database**: PostgreSQL (Supabase)
- **ORM**: Prisma
- **Authentication**: Clerk
- **File Storage**: Supabase Storage

### AI & External Services
- **Vision API**: Google Vision API
- **Prompt Generation**: OpenAI GPT-4
- **Image Generation**: Stability AI (optional)
- **Email**: Resend
- **Analytics**: PostHog
- **Payments**: Stripe

### DevOps
- **Hosting**: Vercel
- **Database**: Supabase
- **Monitoring**: Sentry
- **CI/CD**: GitHub Actions

---

## 🔧 Development Setup Steps

### 1. Environment Setup
1. Create Next.js project with TypeScript
2. Install required dependencies
3. Set up Supabase project
4. Configure Clerk authentication
5. Set up development environment variables

### 2. Database Setup
1. Design database schema
2. Set up Prisma ORM
3. Create database migrations
4. Set up seed data
5. Configure database relationships

### 3. Authentication Integration
1. Configure Clerk providers
2. Set up authentication middleware
3. Create user management system
4. Implement role-based access
5. Set up session management

### 4. Core Development
1. Build canvas interface
2. Implement node system
3. Create AI integration
4. Build prompt generation
5. Add file upload system

### 5. Advanced Features
1. Workspace management
2. Team collaboration
3. Template marketplace
4. Subscription system
5. Analytics dashboard

---

## 📊 Key Metrics to Track

### User Metrics
- Daily/Monthly Active Users
- User Retention Rate
- Feature Adoption Rate
- User Onboarding Completion

### Business Metrics
- Monthly Recurring Revenue (MRR)
- Customer Acquisition Cost (CAC)
- Lifetime Value (LTV)
- Churn Rate

### Product Metrics
- Prompts Generated per User
- Canvas Usage Frequency
- Template Download Rate
- Collaboration Feature Usage

### Technical Metrics
- API Response Times
- Error Rates
- Uptime
- Database Performance

---

## 🚀 Launch Strategy

### Pre-Launch (Weeks 13-14)
- [ ] Beta testing with 20 users
- [ ] Feedback collection and iteration
- [ ] Performance optimization
- [ ] Security testing
- [ ] Documentation completion

### Launch (Week 15)
- [ ] Product Hunt launch
- [ ] Social media campaign
- [ ] Content marketing
- [ ] Influencer outreach
- [ ] Press release

### Post-Launch (Week 16+)
- [ ] User feedback analysis
- [ ] Feature iterations
- [ ] Performance monitoring
- [ ] Customer success programs
- [ ] Growth optimization

---

## 💰 Pricing Strategy

### Free Tier
- 10 prompts per month
- 1 workspace
- Basic templates
- Community support

### Creator ($29/month)
- 100 prompts per month
- 3 workspaces
- All templates
- Email support
- Advanced AI features

### Brand ($99/month)
- 500 prompts per month
- Unlimited workspaces
- Team collaboration (5 seats)
- Custom brand personas
- Priority support

### Agency ($299/month)
- 2000 prompts per month
- White-label options
- Unlimited team seats
- API access
- Custom integrations
- Dedicated support

---

## 🎯 Success Criteria

### Phase 1 (MVP)
- [ ] Basic canvas functionality working
- [ ] AI prompt generation operational
- [ ] User authentication complete
- [ ] Core workflow functional

### Phase 2 (Beta)
- [ ] 50 beta users signed up
- [ ] 80% feature completion rate
- [ ] Average session time > 10 minutes
- [ ] Positive user feedback (4+ stars)

### Phase 3 (Launch)
- [ ] 500 users in first month
- [ ] $5K MRR by month 3
- [ ] 20% monthly growth rate
- [ ] Product-market fit indicators

### Phase 4 (Scale)
- [ ] 5,000 users
- [ ] $50K MRR
- [ ] Enterprise customers
- [ ] Profitable unit economics

This development plan provides a comprehensive roadmap for building SnaPrompt from concept to successful SaaS platform. Each phase builds upon the previous one, ensuring steady progress toward a market-ready product.