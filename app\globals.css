@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: #1e293b;
  --primary-foreground: #f8fafc;
  --secondary: #f1f5f9;
  --secondary-foreground: #1e293b;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #1e293b;
  --destructive: #dc2626;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #94a3b8;
  --chart-1: #f59e0b;
  --chart-2: #3b82f6;
  --chart-3: #8b5cf6;
  --chart-4: #10b981;
  --chart-5: #f97316;
  --sidebar: #f8fafc;
  --sidebar-foreground: #0f172a;
  --sidebar-primary: #1e293b;
  --sidebar-primary-foreground: #f8fafc;
  --sidebar-accent: #f1f5f9;
  --sidebar-accent-foreground: #1e293b;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: #94a3b8;
}

.dark {
  --background: #0f172a;
  --foreground: #f8fafc;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;
  --primary: #e2e8f0;
  --primary-foreground: #1e293b;
  --secondary: #334155;
  --secondary-foreground: #f8fafc;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #334155;
  --accent-foreground: #f8fafc;
  --destructive: #ef4444;
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.15);
  --ring: #64748b;
  --chart-1: #8b5cf6;
  --chart-2: #06b6d4;
  --chart-3: #f97316;
  --chart-4: #a855f7;
  --chart-5: #ef4444;
  --sidebar: #1e293b;
  --sidebar-foreground: #f8fafc;
  --sidebar-primary: #8b5cf6;
  --sidebar-primary-foreground: #f8fafc;
  --sidebar-accent: #334155;
  --sidebar-accent-foreground: #f8fafc;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: #64748b;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom React Flow styles */
.react-flow__node {
  cursor: grab;
}

.react-flow__node:active {
  cursor: grabbing;
}

.react-flow__handle {
  width: 12px !important;
  height: 12px !important;
  background: #6b7280 !important;
  border: 2px solid white !important;
  border-radius: 50% !important;
}

.react-flow__handle-top {
  top: -6px !important;
}

.react-flow__handle-bottom {
  bottom: -6px !important;
}

.react-flow__edge-path {
  stroke: #6b7280;
  stroke-width: 2;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 3;
}

/* Custom gradient background for the canvas */
.react-flow__background {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Professional node shadows */
.workflow-node {
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
  transition: all 0.2s ease-in-out;
}

.workflow-node:hover {
  filter: drop-shadow(0 8px 15px rgba(0, 0, 0, 0.15));
  transform: translateY(-2px);
}

/* Colorful connection lines */
.react-flow__edge-path {
  stroke: #8b5cf6;
  stroke-width: 3;
  stroke-dasharray: none;
}

.react-flow__edge.animated .react-flow__edge-path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}
