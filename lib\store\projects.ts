import { create } from 'zustand'
import { createSupabaseClient } from '@/lib/supabase'

export interface Project {
  id: string
  name: string
  description: string | null
  canvas_data: any
  created_at: string
  updated_at: string
  created_by: string
}

interface ProjectStore {
  projects: Project[]
  loading: boolean
  error: string | null
  fetchProjects: (userId: string) => Promise<void>
  createProject: (userId: string, name: string, description?: string, canvasData?: any) => Promise<Project>
  updateProject: (userId: string, id: string, data: Partial<Project>) => Promise<void>
  deleteProject: (userId: string, id: string) => Promise<void>
}

export const useProjectStore = create<ProjectStore>((set, get) => ({
  projects: [],
  loading: false,
  error: null,

  fetchProjects: async (userId: string) => {
    set({ loading: true, error: null })
    try {
      const supabase = createSupabaseClient(userId)
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('updated_at', { ascending: false })

      if (error) {
        console.error('Error fetching projects:', error)
        throw new Error(error.message)
      }
      
      set({ projects: data || [] })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch projects'
      set({ error: errorMessage })
      throw error
    } finally {
      set({ loading: false })
    }
  },

  createProject: async (userId: string, name: string, description?: string, canvasData: any = {}) => {
    try {
      const supabase = createSupabaseClient(userId)
      
      if (!userId) {
        throw new Error('User not authenticated')
      }
      
      // Ensure the data is properly structured
      const projectData = {
        name,
        description: description || null,
        canvas_data: canvasData || {},
        created_by: userId,
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single()

      if (error) {
        console.error('Error creating project:', error)
        throw new Error(error.message)
      }

      if (!data) {
        throw new Error('No data returned from project creation')
      }
      
      // Update local state
      set(state => ({
        projects: [data, ...state.projects]
      }))

      return data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create project'
      console.error('Project creation error:', errorMessage)
      throw new Error(errorMessage)
    }
  },

  updateProject: async (userId: string, id: string, projectData: Partial<Project>) => {
    try {
      const supabase = createSupabaseClient(userId)
      const { error } = await supabase
        .from('projects')
        .update({
          ...projectData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)

      if (error) {
        console.error('Error updating project:', error)
        throw new Error(error.message)
      }

      // Update local state
      set(state => ({
        projects: state.projects.map(p => 
          p.id === id ? { ...p, ...projectData, updated_at: new Date().toISOString() } : p
        )
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update project'
      console.error('Project update error:', errorMessage)
      throw new Error(errorMessage)
    }
  },

  deleteProject: async (userId: string, id: string) => {
    try {
      const supabase = createSupabaseClient(userId)
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting project:', error)
        throw new Error(error.message)
      }

      // Update local state
      set(state => ({
        projects: state.projects.filter(p => p.id !== id)
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete project'
      console.error('Project deletion error:', errorMessage)
      throw new Error(errorMessage)
    }
  }
})) 