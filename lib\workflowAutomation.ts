export interface WorkflowConfig {
  persona: any;
  platform: string;
  aspectRatio: string;
  cameraStyle: string;
  mood: string;
  description: string;
  uploadedImage: string | null;
  promptTypes: string[];
  includeReels: boolean;
  includeCaption: boolean;
  includeAdCopy: boolean;
}

export interface WorkflowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: any;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
}

export function generateWorkflowFromConfig(config: WorkflowConfig): { nodes: WorkflowNode[], edges: WorkflowEdge[] } {
  const nodes: WorkflowNode[] = [];
  const edges: WorkflowEdge[] = [];

  let currentX = 300; // Center horizontally
  let currentY = 100;
  const nodeSpacing = 200; // Vertical spacing between nodes
  const nodeWidth = 200;
  let previousNodeId = '';

  // 1. Start Node
  const startNode: WorkflowNode = {
    id: 'start-node',
    type: 'task',
    position: { x: currentX, y: currentY },
    data: {
      title: 'Start Workflow',
      description: 'Initialize AI prompt generation',
      type: 'start',
      status: 'pending'
    }
  };
  nodes.push(startNode);
  previousNodeId = startNode.id;
  currentY += nodeSpacing; // Change to vertical spacing

  // 2. Brand Persona Node
  const personaNode: WorkflowNode = {
    id: 'persona-node',
    type: 'persona',
    position: { x: currentX, y: currentY },
    data: {
      title: 'Brand Persona',
      description: `Using ${config.persona.name}`,
      type: 'persona',
      selectedPersona: config.persona,
      status: 'ready'
    }
  };
  nodes.push(personaNode);

  edges.push({
    id: `edge-${previousNodeId}-${personaNode.id}`,
    source: previousNodeId,
    target: personaNode.id,
    type: 'smoothstep'
  });
  previousNodeId = personaNode.id;
  currentY += nodeSpacing;

  // 3. Image Upload Node (if image provided)
  if (config.uploadedImage) {
    const imageNode: WorkflowNode = {
      id: 'image-node',
      type: 'image',
      position: { x: currentX, y: currentY },
      data: {
        title: 'Image Analysis',
        description: 'Reference image uploaded',
        type: 'image',
        uploadedImage: config.uploadedImage,
        status: 'ready'
      }
    };
    nodes.push(imageNode);
    
    edges.push({
      id: `edge-${previousNodeId}-${imageNode.id}`,
      source: previousNodeId,
      target: imageNode.id,
      type: 'smoothstep'
    });
    previousNodeId = imageNode.id;
    currentY += nodeSpacing;
  }

  // 4. Visual Prompt Node (always included)
  const visualPromptNode: WorkflowNode = {
    id: 'visual-prompt-node',
    type: 'prompt',
    position: { x: currentX, y: currentY },
    data: {
      title: 'Visual Prompt',
      description: `Generate ${config.platform} visuals`,
      type: 'visual',
      selectedPlatform: config.platform,
      selectedAspectRatio: config.aspectRatio,
      cameraStyle: config.cameraStyle,
      mood: config.mood,
      config: {
        platforms: [config.platform],
        aspectRatios: [config.aspectRatio],
        cameraStyles: [config.cameraStyle]
      },
      status: 'pending'
    }
  };
  nodes.push(visualPromptNode);
  
  edges.push({
    id: `edge-${previousNodeId}-${visualPromptNode.id}`,
    source: previousNodeId,
    target: visualPromptNode.id,
    type: 'smoothstep'
  });
  previousNodeId = visualPromptNode.id;

  // 5. Additional Content Nodes (sequential layout)
  const contentNodes = [];
  currentY += nodeSpacing;

  if (config.includeReels) {
    const reelsNode: WorkflowNode = {
      id: 'reels-node',
      type: 'prompt',
      position: { x: currentX, y: currentY },
      data: {
        title: 'Reels Script',
        description: 'Generate video script',
        type: 'reels',
        selectedPlatform: config.platform,
        status: 'pending'
      }
    };
    nodes.push(reelsNode);
    contentNodes.push(reelsNode);

    edges.push({
      id: `edge-${visualPromptNode.id}-${reelsNode.id}`,
      source: visualPromptNode.id,
      target: reelsNode.id,
      type: 'smoothstep'
    });
    previousNodeId = reelsNode.id;
    currentY += nodeSpacing;
  }

  if (config.includeCaption) {
    const captionNode: WorkflowNode = {
      id: 'caption-node',
      type: 'prompt',
      position: { x: currentX, y: currentY },
      data: {
        title: 'Caption Generator',
        description: 'Create engaging captions',
        type: 'caption',
        selectedPlatform: config.platform,
        status: 'pending'
      }
    };
    nodes.push(captionNode);
    contentNodes.push(captionNode);

    const sourceNode = contentNodes.length > 1 ? contentNodes[contentNodes.length - 2].id : visualPromptNode.id;
    edges.push({
      id: `edge-${sourceNode}-${captionNode.id}`,
      source: sourceNode,
      target: captionNode.id,
      type: 'smoothstep'
    });
    previousNodeId = captionNode.id;
    currentY += nodeSpacing;
  }

  if (config.includeAdCopy) {
    const adNode: WorkflowNode = {
      id: 'ad-node',
      type: 'prompt',
      position: { x: currentX, y: currentY },
      data: {
        title: 'Ad Copy',
        description: 'Generate advertising copy',
        type: 'ad',
        selectedPlatform: config.platform,
        status: 'pending'
      }
    };
    nodes.push(adNode);
    contentNodes.push(adNode);

    const sourceNode = contentNodes.length > 1 ? contentNodes[contentNodes.length - 2].id : visualPromptNode.id;
    edges.push({
      id: `edge-${sourceNode}-${adNode.id}`,
      source: sourceNode,
      target: adNode.id,
      type: 'smoothstep'
    });
    previousNodeId = adNode.id;
    currentY += nodeSpacing;
  }

  // 6. End Node
  if (contentNodes.length === 0) {
    currentY += nodeSpacing;
  }

  const endNode: WorkflowNode = {
    id: 'end-node',
    type: 'task',
    position: { x: currentX, y: currentY },
    data: {
      title: 'Workflow Complete',
      description: 'All prompts generated',
      type: 'end',
      status: 'pending'
    }
  };
  nodes.push(endNode);

  // Connect the last node to end node
  const lastNodeId = contentNodes.length > 0 ? previousNodeId : visualPromptNode.id;
  edges.push({
    id: `edge-${lastNodeId}-${endNode.id}`,
    source: lastNodeId,
    target: endNode.id,
    type: 'smoothstep'
  });

  return { nodes, edges };
}

export async function executeWorkflowStep(nodeId: string, nodeData: any): Promise<any> {
  const { generateWithGemini } = await import('./ai/geminiApi');

  try {
    let result = '';

    // Use Gemini API for actual AI generation
    if (['visual', 'reels', 'caption', 'ad'].includes(nodeData.type)) {
      result = await generateWithGemini(nodeData.type, {
        persona: nodeData.selectedPersona || { name: 'Brand', description: 'Professional brand' },
        platform: nodeData.selectedPlatform || 'instagram',
        aspectRatio: nodeData.selectedAspectRatio || '1:1',
        cameraStyle: nodeData.cameraStyle || 'professional',
        mood: nodeData.mood || 'professional',
        description: nodeData.description || 'Create engaging content',
        uploadedImage: nodeData.uploadedImage || null
      });
    } else {
      result = 'Step completed successfully';
    }

    return {
      success: true,
      result,
      timestamp: new Date().toISOString(),
      nodeId
    };
  } catch (error) {
    console.error('Error executing workflow step:', error);

    // Fallback to mock responses if Gemini fails
    let fallbackResult = '';
    switch (nodeData.type) {
      case 'visual':
        fallbackResult = `📸 Professional ${nodeData.selectedPlatform || 'social media'} visual prompt: High-quality ${nodeData.cameraStyle || 'professional'} photography featuring ${nodeData.mood || 'brand-appropriate'} aesthetic, optimized for maximum engagement.`;
        break;
      case 'reels':
        fallbackResult = `🎬 Engaging Reels script: Hook (0-3s): Eye-catching opener, Content (3-15s): Value-driven message, CTA (15-30s): Clear action step. Optimized for ${nodeData.selectedPlatform || 'social media'} algorithm.`;
        break;
      case 'caption':
        fallbackResult = `✨ Compelling caption: Attention-grabbing hook, value proposition, brand voice alignment, strategic hashtags, and clear call-to-action for ${nodeData.selectedPlatform || 'social media'}.`;
        break;
      case 'ad':
        fallbackResult = `🎯 High-converting ad copy: Headline that stops scroll, benefit-focused body text, social proof elements, urgency triggers, and compelling CTA for ${nodeData.selectedPlatform || 'social media'} ads.`;
        break;
      default:
        fallbackResult = 'Step completed successfully';
    }

    return {
      success: true,
      result: fallbackResult,
      timestamp: new Date().toISOString(),
      nodeId,
      error: 'Used fallback response'
    };
  }
}
