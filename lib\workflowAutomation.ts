export interface WorkflowConfig {
  persona: any;
  platform: string;
  aspectRatio: string;
  cameraStyle: string;
  mood: string;
  description: string;
  uploadedImage: string | null;
  promptTypes: string[];
  includeReels: boolean;
  includeCaption: boolean;
  includeAdCopy: boolean;
}

export interface WorkflowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: any;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
}

export function generateWorkflowFromConfig(config: WorkflowConfig): { nodes: WorkflowNode[], edges: WorkflowEdge[] } {
  const nodes: WorkflowNode[] = [];
  const edges: WorkflowEdge[] = [];
  
  let currentX = 100;
  let currentY = 100;
  const nodeSpacing = 350;
  let previousNodeId = '';

  // 1. Start Node
  const startNode: WorkflowNode = {
    id: 'start-node',
    type: 'task',
    position: { x: currentX, y: currentY },
    data: {
      title: 'Start Workflow',
      description: 'Initialize AI prompt generation',
      type: 'start',
      status: 'pending'
    }
  };
  nodes.push(startNode);
  previousNodeId = startNode.id;
  currentX += nodeSpacing;

  // 2. Brand Persona Node
  const personaNode: WorkflowNode = {
    id: 'persona-node',
    type: 'persona',
    position: { x: currentX, y: currentY },
    data: {
      title: 'Brand Persona',
      description: `Using ${config.persona.name}`,
      type: 'persona',
      selectedPersona: config.persona,
      status: 'ready'
    }
  };
  nodes.push(personaNode);
  
  edges.push({
    id: `edge-${previousNodeId}-${personaNode.id}`,
    source: previousNodeId,
    target: personaNode.id
  });
  previousNodeId = personaNode.id;
  currentX += nodeSpacing;

  // 3. Image Upload Node (if image provided)
  if (config.uploadedImage) {
    const imageNode: WorkflowNode = {
      id: 'image-node',
      type: 'image',
      position: { x: currentX, y: currentY },
      data: {
        title: 'Image Analysis',
        description: 'Reference image uploaded',
        type: 'image',
        uploadedImage: config.uploadedImage,
        status: 'ready'
      }
    };
    nodes.push(imageNode);
    
    edges.push({
      id: `edge-${previousNodeId}-${imageNode.id}`,
      source: previousNodeId,
      target: imageNode.id
    });
    previousNodeId = imageNode.id;
    currentX += nodeSpacing;
  }

  // 4. Visual Prompt Node (always included)
  const visualPromptNode: WorkflowNode = {
    id: 'visual-prompt-node',
    type: 'prompt',
    position: { x: currentX, y: currentY },
    data: {
      title: 'Visual Prompt',
      description: `Generate ${config.platform} visuals`,
      type: 'visual',
      selectedPlatform: config.platform,
      selectedAspectRatio: config.aspectRatio,
      cameraStyle: config.cameraStyle,
      mood: config.mood,
      config: {
        platforms: [config.platform],
        aspectRatios: [config.aspectRatio],
        cameraStyles: [config.cameraStyle]
      },
      status: 'pending'
    }
  };
  nodes.push(visualPromptNode);
  
  edges.push({
    id: `edge-${previousNodeId}-${visualPromptNode.id}`,
    source: previousNodeId,
    target: visualPromptNode.id
  });
  previousNodeId = visualPromptNode.id;

  // 5. Additional Content Nodes (parallel layout)
  const contentNodes = [];
  let contentY = currentY + 200;
  
  if (config.includeReels) {
    const reelsNode: WorkflowNode = {
      id: 'reels-node',
      type: 'prompt',
      position: { x: currentX, y: contentY },
      data: {
        title: 'Reels Script',
        description: 'Generate video script',
        type: 'reels',
        selectedPlatform: config.platform,
        status: 'pending'
      }
    };
    nodes.push(reelsNode);
    contentNodes.push(reelsNode);
    
    edges.push({
      id: `edge-${visualPromptNode.id}-${reelsNode.id}`,
      source: visualPromptNode.id,
      target: reelsNode.id
    });
    contentY += 150;
  }

  if (config.includeCaption) {
    const captionNode: WorkflowNode = {
      id: 'caption-node',
      type: 'prompt',
      position: { x: currentX, y: contentY },
      data: {
        title: 'Caption Generator',
        description: 'Create engaging captions',
        type: 'caption',
        selectedPlatform: config.platform,
        status: 'pending'
      }
    };
    nodes.push(captionNode);
    contentNodes.push(captionNode);
    
    edges.push({
      id: `edge-${visualPromptNode.id}-${captionNode.id}`,
      source: visualPromptNode.id,
      target: captionNode.id
    });
    contentY += 150;
  }

  if (config.includeAdCopy) {
    const adNode: WorkflowNode = {
      id: 'ad-node',
      type: 'prompt',
      position: { x: currentX, y: contentY },
      data: {
        title: 'Ad Copy',
        description: 'Generate advertising copy',
        type: 'ad',
        selectedPlatform: config.platform,
        status: 'pending'
      }
    };
    nodes.push(adNode);
    contentNodes.push(adNode);
    
    edges.push({
      id: `edge-${visualPromptNode.id}-${adNode.id}`,
      source: visualPromptNode.id,
      target: adNode.id
    });
  }

  // 6. End Node
  currentX += nodeSpacing;
  const endNode: WorkflowNode = {
    id: 'end-node',
    type: 'task',
    position: { x: currentX, y: currentY },
    data: {
      title: 'Workflow Complete',
      description: 'All prompts generated',
      type: 'end',
      status: 'pending'
    }
  };
  nodes.push(endNode);

  // Connect content nodes to end node
  if (contentNodes.length > 0) {
    contentNodes.forEach(contentNode => {
      edges.push({
        id: `edge-${contentNode.id}-${endNode.id}`,
        source: contentNode.id,
        target: endNode.id
      });
    });
  } else {
    edges.push({
      id: `edge-${visualPromptNode.id}-${endNode.id}`,
      source: visualPromptNode.id,
      target: endNode.id
    });
  }

  return { nodes, edges };
}

export function executeWorkflowStep(nodeId: string, nodeData: any): Promise<any> {
  return new Promise((resolve) => {
    // Simulate AI processing time
    setTimeout(() => {
      let result = '';
      
      switch (nodeData.type) {
        case 'visual':
          result = `Professional ${nodeData.selectedPlatform} visual prompt: High-quality ${nodeData.cameraStyle} photography featuring ${nodeData.mood || 'brand-appropriate'} aesthetic, ${nodeData.selectedAspectRatio} aspect ratio, optimized for ${nodeData.selectedPlatform} engagement.`;
          break;
        case 'reels':
          result = `Engaging Reels script: Hook (0-3s): Eye-catching opener, Content (3-15s): Value-driven message, CTA (15-30s): Clear action step. Optimized for ${nodeData.selectedPlatform} algorithm.`;
          break;
        case 'caption':
          result = `Compelling caption: Attention-grabbing hook, value proposition, brand voice alignment, strategic hashtags, and clear call-to-action for ${nodeData.selectedPlatform}.`;
          break;
        case 'ad':
          result = `High-converting ad copy: Headline that stops scroll, benefit-focused body text, social proof elements, urgency triggers, and compelling CTA for ${nodeData.selectedPlatform} ads.`;
          break;
        default:
          result = 'Step completed successfully';
      }
      
      resolve({
        success: true,
        result,
        timestamp: new Date().toISOString()
      });
    }, 1500 + Math.random() * 1000); // 1.5-2.5 seconds
  });
}
