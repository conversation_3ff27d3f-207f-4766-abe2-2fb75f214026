export interface AdCopyConfig {
  persona: any;
  platform: string;
  mood: string;
  description: string;
  adType?: string;
  objective?: string;
  targetAudience?: string;
  budget?: string;
}

export function generateAdCopyPrompt(config: AdCopyConfig): string {
  const { 
    persona, 
    platform, 
    mood, 
    description, 
    adType = 'conversion', 
    objective = 'drive sales',
    targetAudience = 'target demographic',
    budget = 'standard'
  } = config;

  const platformSpecifics = {
    facebook: {
      formats: 'single image, carousel, video, collection ads',
      headlines: '40 characters (mobile), 25 characters (desktop)',
      primaryText: '125 characters before truncation',
      features: 'detailed targeting, lookalike audiences, retargeting'
    },
    instagram: {
      formats: 'photo, video, carousel, stories, reels ads',
      headlines: '40 characters',
      primaryText: '125 characters before truncation',
      features: 'visual-first, influencer partnerships, shopping tags'
    },
    google: {
      formats: 'search ads, display ads, shopping ads, video ads',
      headlines: '30 characters per headline (3 headlines)',
      descriptions: '90 characters per description (2 descriptions)',
      features: 'keyword targeting, search intent, quality score'
    },
    linkedin: {
      formats: 'sponsored content, message ads, dynamic ads',
      headlines: '150 characters',
      introText: '600 characters',
      features: 'professional targeting, B2B focus, job title targeting'
    },
    tiktok: {
      formats: 'in-feed ads, brand takeover, hashtag challenges',
      headlines: '12-40 characters',
      adText: '80 characters',
      features: 'native content style, trending sounds, user-generated content'
    }
  };

  const currentPlatform = platformSpecifics[platform as keyof typeof platformSpecifics] || platformSpecifics.facebook;

  const adObjectives = {
    awareness: 'brand awareness and reach',
    consideration: 'traffic and engagement',
    conversion: 'sales and lead generation',
    retention: 'customer loyalty and repeat purchases'
  };

  return `Create high-converting ${platform} ad copy for ${persona.name || 'the brand'}.

**Campaign Brief:**
${description}

**Brand Voice:** ${persona.description || 'Professional and engaging'}
**Mood/Tone:** ${mood}
**Platform:** ${platform.charAt(0).toUpperCase() + platform.slice(1)}
**Ad Type:** ${adType}
**Objective:** ${objective}
**Target Audience:** ${targetAudience}

**Platform Specifications:**
- Available formats: ${currentPlatform.formats}
- Headline limits: ${currentPlatform.headlines}
- Text limits: ${currentPlatform.primaryText || currentPlatform.descriptions || 'Platform standard'}
- Key features: ${currentPlatform.features}

**Ad Copy Components:**

**HEADLINE:**
- Attention-grabbing and benefit-focused
- Include primary value proposition
- Stay within character limits: ${currentPlatform.headlines}
- Use power words that drive action

**PRIMARY TEXT/DESCRIPTION:**
- Hook readers in first few words
- Clearly communicate the offer/benefit
- Address pain points of ${targetAudience}
- Create urgency or scarcity when appropriate
- Maintain ${mood} tone throughout

**CALL-TO-ACTION:**
- Clear, action-oriented button text
- Align with campaign objective: ${objective}
- Platform-optimized CTA options
- Create sense of urgency

**SOCIAL PROOF ELEMENTS:**
- Customer testimonials or reviews
- Usage statistics or social proof
- Awards, certifications, or credentials
- Trust signals relevant to ${targetAudience}

**TARGETING CONSIDERATIONS:**
- Audience: ${targetAudience}
- Platform features: ${currentPlatform.features}
- Budget optimization: ${budget}
- Conversion tracking setup

**COMPLIANCE & BEST PRACTICES:**
- Follow ${platform} advertising policies
- Avoid prohibited content or claims
- Ensure landing page alignment
- Mobile-first optimization
- A/B testing recommendations

**CREATIVE SUGGESTIONS:**
- Visual elements that complement copy
- Video/image recommendations
- Brand consistency guidelines
- Seasonal or trending elements

Generate multiple variations for A/B testing, including different hooks, benefits, and CTAs optimized for ${platform} advertising success.`;
}

export function generateAdCopyWithGemini(config: AdCopyConfig): Promise<string> {
  const prompt = generateAdCopyPrompt(config);
  
  // This will be replaced with actual Gemini API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(`🎯 ${config.platform.toUpperCase()} AD COPY VARIATIONS

**VARIATION A - Benefit-Focused:**
Headline: "Transform Your ${config.description} in 30 Days"
Primary Text: Stop struggling with ${config.description.toLowerCase()}. Our proven system has helped 10,000+ customers achieve results. Limited time offer - 50% off!
CTA: "Get Started Now"

**VARIATION B - Problem-Solution:**
Headline: "Finally, A Solution That Works"
Primary Text: Tired of ${config.description.toLowerCase()} that doesn't deliver? Join thousands who've discovered the easier way. See results in just 7 days or money back.
CTA: "Try Risk-Free"

**VARIATION C - Social Proof:**
Headline: "Join 50,000+ Happy Customers"
Primary Text: "Best investment I've made!" - Sarah K. ⭐⭐⭐⭐⭐ Don't miss out on what everyone's talking about. Limited spots available.
CTA: "Join Now"

**Targeting Notes:**
- Age: 25-45
- Interests: ${config.description}
- Behavior: Online shoppers
- Lookalike: Existing customers

**Creative Suggestions:**
- Use ${config.mood} color scheme
- Include customer testimonials
- Show before/after results
- Mobile-optimized visuals`);
    }, 2000);
  });
}
